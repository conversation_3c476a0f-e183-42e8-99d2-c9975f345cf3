@extends('frontend.layout')

@section('title', 'My Account - E-Store')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800">My Account</h1>
        <p class="text-gray-600">Welcome back, {{ $user->name }}!</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Navigation -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <nav class="space-y-2">
                    <a href="{{ route('account.dashboard') }}" 
                       class="flex items-center px-3 py-2 text-blue-600 bg-blue-50 rounded-md">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>
                    <a href="{{ route('account.orders') }}" 
                       class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-shopping-bag mr-3"></i>
                        My Orders
                    </a>
                    <a href="{{ route('account.profile') }}" 
                       class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-user mr-3"></i>
                        Profile
                    </a>
                    <form action="{{ route('logout') }}" method="POST" class="mt-4">
                        @csrf
                        <button type="submit" 
                                class="flex items-center w-full px-3 py-2 text-red-600 hover:bg-red-50 rounded-md">
                            <i class="fas fa-sign-out-alt mr-3"></i>
                            Logout
                        </button>
                    </form>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-3">
            <!-- Account Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-shopping-cart text-blue-600 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Orders</p>
                            <p class="text-2xl font-bold text-gray-800">{{ $orderStats['total_orders'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-yellow-600 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending</p>
                            <p class="text-2xl font-bold text-gray-800">{{ $orderStats['pending_orders'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Completed</p>
                            <p class="text-2xl font-bold text-gray-800">{{ $orderStats['completed_orders'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-dollar-sign text-purple-600 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Spent</p>
                            <p class="text-2xl font-bold text-gray-800">${{ number_format($orderStats['total_spent'], 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-800">Recent Orders</h2>
                        <a href="{{ route('account.orders') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                            View All Orders
                        </a>
                    </div>
                </div>

                @if($recentOrders->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($recentOrders as $order)
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800">
                                            Order #{{ $order->order_number }}
                                        </h3>
                                        <p class="text-sm text-gray-600">
                                            Placed on {{ $order->created_at->format('M d, Y') }}
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-gray-800">
                                            ${{ number_format($order->total_amount, 2) }}
                                        </p>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                                            @elseif($order->status === 'shipped') bg-purple-100 text-purple-800
                                            @elseif($order->status === 'completed') bg-green-100 text-green-800
                                            @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800
                                            @endif">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-4">
                                    @foreach($order->orderItems->take(3) as $item)
                                        <div class="flex items-center space-x-2">
                                            @if($item->product && $item->product->main_image)
                                                <img src="{{ $item->product->main_image }}" 
                                                     alt="{{ $item->product->name }}" 
                                                     class="w-12 h-12 object-cover rounded">
                                            @else
                                                <div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <p class="text-sm font-medium text-gray-800">
                                                    {{ $item->product ? $item->product->name : 'Product not found' }}
                                                </p>
                                                <p class="text-xs text-gray-600">Qty: {{ $item->quantity }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                    
                                    @if($order->orderItems->count() > 3)
                                        <div class="text-sm text-gray-600">
                                            +{{ $order->orderItems->count() - 3 }} more items
                                        </div>
                                    @endif
                                </div>

                                <div class="mt-4 flex justify-end">
                                    <a href="{{ route('account.orders.show', $order) }}" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="p-6 text-center">
                        <i class="fas fa-shopping-bag text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-600 mb-2">No orders yet</h3>
                        <p class="text-gray-500 mb-4">You haven't placed any orders yet.</p>
                        <a href="{{ route('products.index') }}" 
                           class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                            Start Shopping
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
