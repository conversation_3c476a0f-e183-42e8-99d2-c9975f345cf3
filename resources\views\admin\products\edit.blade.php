@extends('admin.layout')

@section('title', 'Edit Produk')
@section('page-title', 'Edit Produk')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Edit: {{ $product->name }}</h3>
                <div class="flex items-center space-x-2">
                    <a href="{{ route('admin.products.show', $product) }}"
                       class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-eye mr-2"></i>Lihat Produk
                    </a>
                    <a href="{{ route('admin.products.index') }}"
                       class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-arrow-left mr-2"></i><PERSON><PERSON><PERSON> ke Produk
                    </a>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form method="POST" action="{{ route('admin.products.update', $product) }}" class="p-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Informasi Dasar</h4>

                        <div class="space-y-4">
                            <!-- Product Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nama Produk *
                                </label>
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="{{ old('name', $product->name) }}"
                                       required
                                       placeholder="contoh: iPhone 15 Pro, Samsung Galaxy S24, MacBook Pro 16&quot;"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('name') border-red-500 @enderror">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- SKU -->
                            <div>
                                <label for="sku" class="block text-sm font-medium text-gray-700 mb-2">
                                    SKU (Kode Produk) *
                                </label>
                                <input type="text"
                                       id="sku"
                                       name="sku"
                                       value="{{ old('sku', $product->sku) }}"
                                       required
                                       placeholder="contoh: IP15PRO, SGS24, MBP16"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('sku') border-red-500 @enderror">
                                @error('sku')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Category -->
                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Kategori *
                                </label>
                                <select id="category_id"
                                        name="category_id"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('category_id') border-red-500 @enderror">
                                    <option value="">Pilih kategori produk</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Short Description -->
                            <div>
                                <label for="short_description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Deskripsi Singkat
                                </label>
                                <textarea id="short_description"
                                          name="short_description"
                                          rows="2"
                                          placeholder="Ringkasan produk singkat untuk tampilan katalog..."
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('short_description') border-red-500 @enderror">{{ old('short_description', $product->short_description) }}</textarea>
                                @error('short_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Harga -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Harga</h4>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <!-- Regular Price -->
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                                    Harga Normal (IDR) *
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-2 text-gray-500">Rp</span>
                                    <input type="number"
                                           id="price"
                                           name="price"
                                           value="{{ old('price', $product->price) }}"
                                           step="1000"
                                           min="0"
                                           required
                                           placeholder="2500000"
                                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('price') border-red-500 @enderror">
                                </div>
                                @error('price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Sale Price -->
                            <div>
                                <label for="sale_price" class="block text-sm font-medium text-gray-700 mb-2">
                                    Harga Diskon (IDR)
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-2 text-gray-500">Rp</span>
                                    <input type="number"
                                           id="sale_price"
                                           name="sale_price"
                                           value="{{ old('sale_price', $product->sale_price) }}"
                                           step="1000"
                                           min="0"
                                           placeholder="2200000"
                                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('sale_price') border-red-500 @enderror">
                                </div>
                                @error('sale_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Stok & Inventori -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Stok & Inventori</h4>
                        
                        <div class="space-y-4">
                            <!-- Stock Quantity -->
                            <div>
                                <label for="stock_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                    Jumlah Stok *
                                </label>
                                <input type="number" 
                                       id="stock_quantity" 
                                       name="stock_quantity" 
                                       value="{{ old('stock_quantity', $product->stock_quantity) }}" 
                                       min="0" 
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('stock_quantity') border-red-500 @enderror">
                                @error('stock_quantity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Manage Stock -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="manage_stock" 
                                       name="manage_stock" 
                                       value="1"
                                       {{ old('manage_stock', $product->manage_stock) ? 'checked' : '' }}
                                       class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                                <label for="manage_stock" class="ml-2 block text-sm text-gray-900">
                                    Kelola stok otomatis
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Properti Fisik -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Properti Fisik</h4>
                        
                        <div class="space-y-4">
                            <!-- Weight -->
                            <div>
                                <label for="weight" class="block text-sm font-medium text-gray-700 mb-2">
                                    Berat (kg)
                                </label>
                                <input type="number" 
                                       id="weight" 
                                       name="weight" 
                                       value="{{ old('weight', $product->weight) }}" 
                                       step="0.01" 
                                       min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('weight') border-red-500 @enderror">
                                @error('weight')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Dimensions -->
                            <div>
                                <label for="dimensions" class="block text-sm font-medium text-gray-700 mb-2">
                                    Dimensions
                                </label>
                                <input type="text" 
                                       id="dimensions" 
                                       name="dimensions" 
                                       value="{{ old('dimensions', $product->dimensions) }}" 
                                       placeholder="e.g., 10 x 5 x 2 cm"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('dimensions') border-red-500 @enderror">
                                @error('dimensions')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Status Produk -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Status Produk</h4>
                        
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1"
                                       {{ old('is_active', $product->is_active) ? 'checked' : '' }}
                                       class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                    Aktif (terlihat oleh pelanggan)
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="is_featured" 
                                       name="is_featured" 
                                       value="1"
                                       {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}
                                       class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                                <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                    Produk unggulan
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Full Width Sections -->
            <div class="mt-8 space-y-6">
                <!-- Description -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Deskripsi Produk</h4>
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi Lengkap *
                        </label>
                        <textarea id="description"
                                  name="description"
                                  rows="6"
                                  required
                                  placeholder="Deskripsi detail produk, spesifikasi, fitur unggulan, dll..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 @error('description') border-red-500 @enderror">{{ old('description', $product->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Gambar Produk -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Gambar Produk</h4>
                    <div class="space-y-3" x-data="{ imageCount: {{ $product->images ? count($product->images) : 1 }} }">
                        <div class="text-sm text-gray-600 mb-3">
                            Tambahkan URL gambar produk. Gambar pertama akan digunakan sebagai gambar utama.
                        </div>

                        @if($product->images && count($product->images) > 0)
                            @foreach($product->images as $index => $image)
                                <div class="flex items-center space-x-2">
                                    <input type="url"
                                           name="images[{{ $index }}]"
                                           value="{{ old('images.' . $index, $image) }}"
                                           placeholder="https://images.unsplash.com/photo-...{{ $index + 1 }}"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    @if($index > 0)
                                        <button type="button"
                                                @click="if(imageCount > 1) imageCount--"
                                                class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-minus-circle"></i>
                                        </button>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <div class="flex items-center space-x-2">
                                <input type="url"
                                       name="images[0]"
                                       value="{{ old('images.0') }}"
                                       placeholder="https://images.unsplash.com/photo-...1"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                            </div>
                        @endif

                        <template x-for="i in imageCount - {{ $product->images ? count($product->images) : 1 }}" :key="i + {{ $product->images ? count($product->images) : 1 }}">
                            <div class="flex items-center space-x-2">
                                <input type="url"
                                       :name="'images[' + (i + {{ $product->images ? count($product->images) : 1 }} - 1) + ']'"
                                       :placeholder="'Image URL ' + (i + {{ $product->images ? count($product->images) : 1 }})"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                <button type="button"
                                        @click="if(imageCount > 1) imageCount--"
                                        class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-minus-circle"></i>
                                </button>
                            </div>
                        </template>

                        <button type="button"
                                @click="imageCount++"
                                class="text-orange-600 hover:text-orange-800 text-sm">
                            <i class="fas fa-plus-circle mr-1"></i>Tambah gambar lain
                        </button>
                    </div>
                </div>

                <!-- Atribut Produk -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Atribut Produk</h4>
                    <div class="space-y-3" x-data="{ attrCount: {{ $product->attributes ? count($product->attributes) : 1 }} }">
                        <div class="text-sm text-gray-600 mb-3">
                            Tambahkan atribut khusus seperti Warna, Ukuran, Kapasitas, dll.
                        </div>

                        @if($product->attributes && count($product->attributes) > 0)
                            @php $attrIndex = 0; @endphp
                            @foreach($product->attributes as $key => $value)
                                <div class="grid grid-cols-2 gap-4">
                                    <input type="text"
                                           name="attributes[{{ $key }}]"
                                           value="{{ old('attributes.' . $key, $key) }}"
                                           placeholder="Nama atribut (contoh: Warna, Kapasitas)"
                                           class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    <div class="flex items-center space-x-2">
                                        <input type="text"
                                               name="attributes[{{ $key }}]"
                                               value="{{ old('attributes.' . $key, $value) }}"
                                               placeholder="Nilai atribut (contoh: Space Gray, 256GB)"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                        @if($attrIndex > 0)
                                            <button type="button"
                                                    @click="if(attrCount > 1) attrCount--"
                                                    class="text-red-600 hover:text-red-800">
                                                <i class="fas fa-minus-circle"></i>
                                            </button>
                                        @endif
                                    </div>
                                </div>
                                @php $attrIndex++; @endphp
                            @endforeach
                        @else
                            <div class="grid grid-cols-2 gap-4">
                                <input type="text"
                                       name="attributes[attr_1_name]"
                                       placeholder="Nama atribut (contoh: Warna, Kapasitas)"
                                       class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                <input type="text"
                                       name="attributes[attr_1_value]"
                                       placeholder="Nilai atribut (contoh: Space Gray, 256GB)"
                                       class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                            </div>
                        @endif

                        <template x-for="i in attrCount - {{ $product->attributes ? count($product->attributes) : 1 }}" :key="i + {{ $product->attributes ? count($product->attributes) : 1 }}">
                            <div class="grid grid-cols-2 gap-4">
                                <input type="text"
                                       :name="'attributes[attr_' + (i + {{ $product->attributes ? count($product->attributes) : 1 }}) + '_name]'"
                                       placeholder="Nama atribut (contoh: Warna, Kapasitas)"
                                       class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                <div class="flex items-center space-x-2">
                                    <input type="text"
                                           :name="'attributes[attr_' + (i + {{ $product->attributes ? count($product->attributes) : 1 }}) + '_value]'"
                                           placeholder="Nilai atribut (contoh: Space Gray, 256GB)"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    <button type="button"
                                            @click="if(attrCount > 1) attrCount--"
                                            class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-minus-circle"></i>
                                    </button>
                                </div>
                            </div>
                        </template>

                        <button type="button"
                                @click="attrCount++"
                                class="text-orange-600 hover:text-orange-800 text-sm">
                            <i class="fas fa-plus-circle mr-1"></i>Tambah atribut lain
                        </button>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.products.show', $product) }}"
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-300">
                    Batal
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition duration-300">
                    <i class="fas fa-save mr-2"></i>Update Produk
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Product Info -->
<div class="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
    <h4 class="text-sm font-medium text-gray-900 mb-2">
        <i class="fas fa-info-circle mr-2"></i>Product Information
    </h4>
    <div class="text-sm text-gray-700 grid grid-cols-2 gap-4">
        <div>
            <span class="font-medium">Created:</span> {{ $product->created_at->format('M d, Y H:i') }}
        </div>
        <div>
            <span class="font-medium">Last Updated:</span> {{ $product->updated_at->format('M d, Y H:i') }}
        </div>
        <div>
            <span class="font-medium">Slug:</span> {{ $product->slug }}
        </div>
        <div>
            <span class="font-medium">Status Produk:</span>
            <span class="px-2 py-1 text-xs rounded-full {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                {{ $product->is_active ? 'Active' : 'Inactive' }}
            </span>
        </div>
    </div>
</div>
@endsection
