@extends('frontend.layout')

@section('title', $product->name . ' - Sahabat Rumah')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
            <li><a href="{{ route('home') }}" class="hover:text-pastel-orange">Beranda</a></li>
            <li><i class="fas fa-chevron-right text-xs"></i></li>
            <li><a href="{{ route('products.index') }}" class="hover:text-pastel-orange">Produk</a></li>
            @if($product->category)
                <li><i class="fas fa-chevron-right text-xs"></i></li>
                <li><a href="{{ route('categories.show', $product->category->slug) }}" class="hover:text-pastel-orange">{{ $product->category->name }}</a></li>
            @endif
            <li><i class="fas fa-chevron-right text-xs"></i></li>
            <li class="text-navy-blue font-medium">{{ $product->name }}</li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Product Images -->
        <div>
            @if($product->images && count($product->images) > 0)
                <div x-data="{ activeImage: 0 }">
                    <!-- Main Image -->
                    <div class="mb-4">
                        <img :src="[
                            @foreach($product->images as $index => $image)
                                '{{ $image }}'{{ !$loop->last ? ',' : '' }}
                            @endforeach
                        ][activeImage]" 
                             alt="{{ $product->name }}" 
                             class="w-full h-96 object-cover rounded-lg">
                    </div>
                    
                    <!-- Thumbnail Images -->
                    @if(count($product->images) > 1)
                        <div class="flex space-x-2 overflow-x-auto">
                            @foreach($product->images as $index => $image)
                                <button @click="activeImage = {{ $index }}"
                                        :class="activeImage === {{ $index }} ? 'ring-2 ring-pastel-orange' : ''"
                                        class="flex-shrink-0 w-20 h-20 rounded-md overflow-hidden">
                                    <img src="{{ $image }}" alt="{{ $product->name }}" class="w-full h-full object-cover">
                                </button>
                            @endforeach
                        </div>
                    @endif
                </div>
            @else
                <div class="w-full h-96 bg-gray-200 rounded-lg flex items-center justify-center">
                    <i class="fas fa-image text-gray-400 text-6xl"></i>
                </div>
            @endif
        </div>

        <!-- Product Details -->
        <div>
            <h1 class="text-3xl font-bold text-navy-blue mb-4">{{ $product->name }}</h1>

            @if($product->category)
                <div class="mb-4">
                    <a href="{{ route('categories.show', $product->category->slug) }}"
                       class="inline-block bg-light-orange text-navy-blue text-sm px-3 py-1 rounded-full hover:bg-pastel-orange">
                        {{ $product->category->name }}
                    </a>
                </div>
            @endif

            <!-- Price -->
            <div class="mb-6">
                @if($product->sale_price)
                    <div class="flex items-center space-x-3">
                        <span class="text-3xl font-bold text-red-600">{{ $product->formatted_current_price }}</span>
                        <span class="text-xl text-gray-500 line-through">{{ $product->formatted_price }}</span>
                        <span class="bg-red-100 text-red-800 text-sm px-2 py-1 rounded">
                            Hemat {{ number_format((($product->price - $product->sale_price) / $product->price) * 100, 0) }}%
                        </span>
                    </div>
                @else
                    <span class="text-3xl font-bold text-navy-blue">{{ $product->formatted_price }}</span>
                @endif
            </div>

            <!-- Stock Status -->
            <div class="mb-6">
                @if($product->in_stock)
                    <span class="inline-flex items-center text-green-600">
                        <i class="fas fa-check-circle mr-2"></i>
                        Tersedia
                        @if($product->manage_stock && $product->stock_quantity <= 10)
                            <span class="ml-2 text-orange-600">(Hanya {{ $product->stock_quantity }} tersisa)</span>
                        @endif
                    </span>
                @else
                    <span class="inline-flex items-center text-red-600">
                        <i class="fas fa-times-circle mr-2"></i>
                        Stok Habis
                    </span>
                @endif
            </div>

            <!-- Short Description -->
            @if($product->short_description)
                <div class="mb-6">
                    <p class="text-gray-600 text-lg">{{ $product->short_description }}</p>
                </div>
            @endif

            <!-- Add to Cart -->
            @if($product->in_stock)
                <div class="mb-8" x-data="{ quantity: 1 }">
                    <div class="flex items-center space-x-4 mb-4">
                        <label class="text-sm font-medium text-navy-blue">Jumlah:</label>
                        <div class="flex items-center border border-gray-300 rounded-md">
                            <button @click="quantity = Math.max(1, quantity - 1)"
                                    class="px-3 py-2 text-gray-600 hover:text-pastel-orange">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input x-model="quantity" type="number" min="1"
                                   class="w-16 px-3 py-2 text-center border-0 focus:ring-0">
                            <button @click="quantity++"
                                    class="px-3 py-2 text-gray-600 hover:text-pastel-orange">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <button @click="addToCart({{ $product->id }}, quantity)"
                            class="w-full bg-pastel-orange text-navy-blue py-3 px-6 rounded-lg text-lg font-semibold hover:bg-orange-300 transition duration-300">
                        <i class="fas fa-cart-plus mr-2"></i>
                        Tambah ke Keranjang
                    </button>
                </div>
            @endif

            <!-- Product Details -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-semibold mb-4 text-navy-blue">Detail Produk</h3>
                <div class="space-y-2 text-sm">
                    @if($product->sku)
                        <div class="flex">
                            <span class="w-24 text-gray-600">SKU:</span>
                            <span class="text-gray-800">{{ $product->sku }}</span>
                        </div>
                    @endif
                    @if($product->weight)
                        <div class="flex">
                            <span class="w-24 text-gray-600">Berat:</span>
                            <span class="text-gray-800">{{ $product->weight }} kg</span>
                        </div>
                    @endif
                    @if($product->dimensions)
                        <div class="flex">
                            <span class="w-24 text-gray-600">Dimensi:</span>
                            <span class="text-gray-800">{{ $product->dimensions }}</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Product Description -->
    @if($product->description)
        <div class="mt-16">
            <h2 class="text-2xl font-bold text-navy-blue mb-6">Deskripsi Produk</h2>
            <div class="prose max-w-none text-gray-600">
                {!! nl2br(e($product->description)) !!}
            </div>
        </div>
    @endif

    <!-- Related Products -->
    @if($relatedProducts->count() > 0)
        <div class="mt-16">
            <h2 class="text-2xl font-bold text-navy-blue mb-8">Produk Terkait</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedProducts as $relatedProduct)
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 overflow-hidden">
                        <a href="{{ route('products.show', $relatedProduct->slug) }}">
                            @if($relatedProduct->main_image)
                                <img src="{{ $relatedProduct->main_image }}" alt="{{ $relatedProduct->name }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-4xl"></i>
                                </div>
                            @endif
                        </a>
                        <div class="p-4">
                            <a href="{{ route('products.show', $relatedProduct->slug) }}" class="block">
                                <h3 class="font-semibold text-gray-800 hover:text-blue-600 mb-2">{{ $relatedProduct->name }}</h3>
                            </a>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    @if($relatedProduct->sale_price)
                                        <span class="text-lg font-bold text-red-600">{{ $relatedProduct->formatted_current_price }}</span>
                                        <span class="text-sm text-gray-500 line-through">{{ $relatedProduct->formatted_price }}</span>
                                    @else
                                        <span class="text-lg font-bold text-gray-800">{{ $relatedProduct->formatted_price }}</span>
                                    @endif
                                </div>
                                <button onclick="addToCart({{ $relatedProduct->id }})" 
                                        class="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition duration-300">
                                    <i class="fas fa-cart-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>

<script>
async function addToCart(productId, quantity = 1) {
    const result = await window.cartAPI.add(productId, quantity);
    
    if (result.success) {
        // Update cart count in navigation
        const cartCountElement = document.querySelector('[x-text="cartCount"]');
        if (cartCountElement) {
            cartCountElement.__x.$data.cartCount += quantity;
        }
        
        // Show success message
        showNotification(result.message, 'success');
    } else {
        showNotification(result.message, 'error');
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
