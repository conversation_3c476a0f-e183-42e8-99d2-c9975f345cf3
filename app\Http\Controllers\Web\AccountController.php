<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;

class AccountController extends Controller
{
    public function dashboard(): View
    {
        $user = auth()->user();
        $recentOrders = Order::where('customer_email', $user->email)
            ->with('orderItems.product')
            ->latest()
            ->limit(5)
            ->get();

        $orderStats = [
            'total_orders' => Order::where('customer_email', $user->email)->count(),
            'pending_orders' => Order::where('customer_email', $user->email)->where('status', 'pending')->count(),
            'completed_orders' => Order::where('customer_email', $user->email)->where('status', 'completed')->count(),
            'total_spent' => Order::where('customer_email', $user->email)->where('status', '!=', 'cancelled')->sum('total_amount'),
        ];

        return view('frontend.account.dashboard', compact('user', 'recentOrders', 'orderStats'));
    }

    public function orders(): View
    {
        $user = auth()->user();
        $orders = Order::where('customer_email', $user->email)
            ->with('orderItems.product')
            ->latest()
            ->paginate(10);

        return view('frontend.account.orders', compact('orders'));
    }

    public function orderDetail(Order $order): View
    {
        // Ensure user can only see their own orders
        if ($order->customer_email !== auth()->user()->email) {
            abort(403);
        }

        $order->load('orderItems.product');

        return view('frontend.account.order-detail', compact('order'));
    }

    public function profile(): View
    {
        $user = auth()->user();
        return view('frontend.account.profile', compact('user'));
    }

    public function updateProfile(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'billing_address' => 'nullable|string|max:255',
            'billing_city' => 'nullable|string|max:100',
            'billing_state' => 'nullable|string|max:100',
            'billing_postal_code' => 'nullable|string|max:20',
            'billing_country' => 'nullable|string|max:100',
            'shipping_address' => 'nullable|string|max:255',
            'shipping_city' => 'nullable|string|max:100',
            'shipping_state' => 'nullable|string|max:100',
            'shipping_postal_code' => 'nullable|string|max:20',
            'shipping_country' => 'nullable|string|max:100',
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Check current password if new password is provided
        if ($request->filled('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password'], $validated['current_password']);
        }

        $user->update($validated);

        return back()->with('success', 'Profile updated successfully.');
    }
}
