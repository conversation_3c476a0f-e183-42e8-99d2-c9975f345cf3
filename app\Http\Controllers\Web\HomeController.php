<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\View\View;

class HomeController extends Controller
{
    public function index(): View
    {
        $featuredProducts = Product::with('category')
            ->active()
            ->featured()
            ->inStock()
            ->limit(8)
            ->get();

        $categories = Category::active()
            ->rootCategories()
            ->with('children')
            ->orderBy('sort_order')
            ->get();

        $newProducts = Product::with('category')
            ->active()
            ->inStock()
            ->latest()
            ->limit(4)
            ->get();

        return view('frontend.home', compact('featuredProducts', 'categories', 'newProducts'));
    }
}
