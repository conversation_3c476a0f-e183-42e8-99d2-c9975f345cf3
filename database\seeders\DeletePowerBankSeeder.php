<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;

class DeletePowerBankSeeder extends Seeder
{
    /**
     * Delete Power Bank 20000mAh product
     */
    public function run(): void
    {
        $this->command->info('🗑️  Menghapus produk Power Bank 20000mAh...');
        $this->command->info('');

        // Cari produk Power Bank 20000mAh
        $product = Product::where('name', 'LIKE', '%Power Bank 20000mAh%')->first();

        if (!$product) {
            $this->command->error('❌ Produk Power Bank 20000mAh tidak ditemukan!');
            
            // Tampilkan produk yang mengandung kata "Power Bank"
            $this->command->info('');
            $this->command->info('🔍 Mencari produk yang mengandung "Power Bank"...');
            $relatedProducts = Product::where('name', 'LIKE', '%Power Bank%')->get(['id', 'name', 'sku']);
            
            if ($relatedProducts->count() > 0) {
                $this->command->info('📋 Produk terkait yang ditemukan:');
                foreach ($relatedProducts as $prod) {
                    $this->command->line("   - ID: {$prod->id} | {$prod->name} | SKU: {$prod->sku}");
                }
            } else {
                $this->command->info('   Tidak ada produk Power Bank yang ditemukan.');
            }
            
            return;
        }

        $this->command->info("✅ Produk ditemukan:");
        $this->command->info("   - Nama: {$product->name}");
        $this->command->info("   - ID: {$product->id}");
        $this->command->info("   - SKU: {$product->sku}");
        $this->command->info("   - Harga: Rp " . number_format($product->price, 0, ',', '.'));
        if ($product->sale_price) {
            $this->command->info("   - Harga Diskon: Rp " . number_format($product->sale_price, 0, ',', '.'));
        }
        $this->command->info("   - Stok: {$product->stock_quantity}");
        $this->command->info('');
        
        // Cek apakah produk memiliki order items
        $orderItemsCount = $product->orderItems()->count();
        
        if ($orderItemsCount > 0) {
            $this->command->error("❌ Tidak dapat menghapus produk Power Bank 20000mAh!");
            $this->command->error("   Produk ini memiliki {$orderItemsCount} order item(s).");
            $this->command->error("   Produk sudah pernah dipesan oleh pelanggan.");
            
            $this->command->info('');
            $this->command->info('💡 Alternatif:');
            $this->command->info('   1. Nonaktifkan produk (jika fitur tersedia)');
            $this->command->info('   2. Ubah status stok menjadi "Habis"');
            $this->command->info('   3. Sembunyikan dari katalog');
            $this->command->info('   4. Tandai sebagai discontinued');
            
            return;
        }

        $this->command->info("🔍 Memeriksa dependensi...");
        $this->command->info("✅ Tidak ada order yang terkait dengan produk ini.");
        $this->command->info('');
        $this->command->info("🗑️  Menghapus produk Power Bank 20000mAh...");
        
        try {
            $productName = $product->name;
            $productSku = $product->sku;
            $productPrice = 'Rp ' . number_format($product->price, 0, ',', '.');
            
            $product->delete();
            
            $this->command->info("✅ Produk '{$productName}' berhasil dihapus!");
            $this->command->info("   SKU: {$productSku}");
            $this->command->info("   Harga: {$productPrice}");
            $this->command->info('');
            
            // Show remaining products count
            $remainingCount = Product::count();
            $this->command->info("📦 Total produk tersisa: {$remainingCount}");
            
            // Show remaining Electronics products
            $electronicsCategory = \App\Models\Category::where('name', 'LIKE', '%Electronics%')->first();
            if ($electronicsCategory) {
                $electronicsCount = Product::where('category_id', $electronicsCategory->id)->count();
                $this->command->info("📱 Total produk Electronics tersisa: {$electronicsCount}");
            }
            
        } catch (\Exception $e) {
            $this->command->error("❌ Error saat menghapus produk: " . $e->getMessage());
        }
        
        $this->command->info('');
        $this->command->info('🏁 Proses penghapusan selesai!');
    }
}
