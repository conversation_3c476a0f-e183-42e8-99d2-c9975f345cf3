<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Get dashboard statistics
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'overview' => $this->getOverviewStats(),
            'sales' => $this->getSalesStats(),
            'products' => $this->getProductStats(),
            'orders' => $this->getOrderStats(),
            'recent_orders' => $this->getRecentOrders(),
            'top_products' => $this->getTopProducts(),
            'low_stock_products' => $this->getLowStockProducts(),
        ];

        return response()->json($stats);
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(): array
    {
        return [
            'total_products' => Product::count(),
            'total_categories' => Category::count(),
            'total_orders' => Order::count(),
            'total_revenue' => Order::whereIn('status', ['delivered', 'shipped'])->sum('total_amount'),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'low_stock_products' => Product::where('manage_stock', true)
                ->where('stock_quantity', '<=', 10)
                ->count(),
        ];
    }

    /**
     * Get sales statistics
     */
    private function getSalesStats(): array
    {
        $today = now()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();
        $thisYear = now()->startOfYear();

        return [
            'today' => [
                'orders' => Order::whereDate('created_at', $today)->count(),
                'revenue' => Order::whereDate('created_at', $today)
                    ->whereIn('status', ['delivered', 'shipped'])
                    ->sum('total_amount'),
            ],
            'this_week' => [
                'orders' => Order::where('created_at', '>=', $thisWeek)->count(),
                'revenue' => Order::where('created_at', '>=', $thisWeek)
                    ->whereIn('status', ['delivered', 'shipped'])
                    ->sum('total_amount'),
            ],
            'this_month' => [
                'orders' => Order::where('created_at', '>=', $thisMonth)->count(),
                'revenue' => Order::where('created_at', '>=', $thisMonth)
                    ->whereIn('status', ['delivered', 'shipped'])
                    ->sum('total_amount'),
            ],
            'this_year' => [
                'orders' => Order::where('created_at', '>=', $thisYear)->count(),
                'revenue' => Order::where('created_at', '>=', $thisYear)
                    ->whereIn('status', ['delivered', 'shipped'])
                    ->sum('total_amount'),
            ],
        ];
    }

    /**
     * Get product statistics
     */
    private function getProductStats(): array
    {
        return [
            'total' => Product::count(),
            'active' => Product::where('is_active', true)->count(),
            'inactive' => Product::where('is_active', false)->count(),
            'featured' => Product::where('is_featured', true)->count(),
            'in_stock' => Product::where('in_stock', true)->count(),
            'out_of_stock' => Product::where('in_stock', false)->count(),
            'low_stock' => Product::where('manage_stock', true)
                ->where('stock_quantity', '<=', 10)
                ->count(),
        ];
    }

    /**
     * Get order statistics
     */
    private function getOrderStats(): array
    {
        return [
            'total' => Order::count(),
            'pending' => Order::where('status', 'pending')->count(),
            'processing' => Order::where('status', 'processing')->count(),
            'shipped' => Order::where('status', 'shipped')->count(),
            'delivered' => Order::where('status', 'delivered')->count(),
            'cancelled' => Order::where('status', 'cancelled')->count(),
        ];
    }

    /**
     * Get recent orders
     */
    private function getRecentOrders(): array
    {
        return Order::with(['orderItems.product'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get top selling products
     */
    private function getTopProducts(): array
    {
        return DB::table('order_items')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.status', '!=', 'cancelled')
            ->select(
                'products.id',
                'products.name',
                'products.sku',
                'products.price',
                DB::raw('SUM(order_items.quantity) as total_sold'),
                DB::raw('SUM(order_items.total_price) as total_revenue')
            )
            ->groupBy('products.id', 'products.name', 'products.sku', 'products.price')
            ->orderBy('total_sold', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get low stock products
     */
    private function getLowStockProducts(): array
    {
        return Product::where('manage_stock', true)
            ->where('stock_quantity', '<=', 10)
            ->orderBy('stock_quantity', 'asc')
            ->limit(10)
            ->get(['id', 'name', 'sku', 'stock_quantity'])
            ->toArray();
    }

    /**
     * Get sales chart data
     */
    public function salesChart(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week'); // week, month, year
        $data = [];

        switch ($period) {
            case 'week':
                $data = $this->getWeeklySalesData();
                break;
            case 'month':
                $data = $this->getMonthlySalesData();
                break;
            case 'year':
                $data = $this->getYearlySalesData();
                break;
        }

        return response()->json($data);
    }

    /**
     * Get weekly sales data
     */
    private function getWeeklySalesData(): array
    {
        $startDate = now()->startOfWeek();
        $data = [];

        for ($i = 0; $i < 7; $i++) {
            $date = $startDate->copy()->addDays($i);
            $orders = Order::whereDate('created_at', $date)
                ->whereIn('status', ['delivered', 'shipped'])
                ->count();
            $revenue = Order::whereDate('created_at', $date)
                ->whereIn('status', ['delivered', 'shipped'])
                ->sum('total_amount');

            $data[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('l'),
                'orders' => $orders,
                'revenue' => (float) $revenue,
            ];
        }

        return $data;
    }

    /**
     * Get monthly sales data
     */
    private function getMonthlySalesData(): array
    {
        $startDate = now()->startOfMonth();
        $daysInMonth = $startDate->daysInMonth;
        $data = [];

        for ($i = 0; $i < $daysInMonth; $i++) {
            $date = $startDate->copy()->addDays($i);
            $orders = Order::whereDate('created_at', $date)
                ->whereIn('status', ['delivered', 'shipped'])
                ->count();
            $revenue = Order::whereDate('created_at', $date)
                ->whereIn('status', ['delivered', 'shipped'])
                ->sum('total_amount');

            $data[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('j'),
                'orders' => $orders,
                'revenue' => (float) $revenue,
            ];
        }

        return $data;
    }

    /**
     * Get yearly sales data
     */
    private function getYearlySalesData(): array
    {
        $data = [];

        for ($i = 1; $i <= 12; $i++) {
            $startDate = now()->startOfYear()->month($i);
            $endDate = $startDate->copy()->endOfMonth();

            $orders = Order::whereBetween('created_at', [$startDate, $endDate])
                ->whereIn('status', ['delivered', 'shipped'])
                ->count();
            $revenue = Order::whereBetween('created_at', [$startDate, $endDate])
                ->whereIn('status', ['delivered', 'shipped'])
                ->sum('total_amount');

            $data[] = [
                'month' => $startDate->format('F'),
                'month_number' => $i,
                'orders' => $orders,
                'revenue' => (float) $revenue,
            ];
        }

        return $data;
    }
}
