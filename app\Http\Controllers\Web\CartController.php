<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class CartController extends Controller
{
    public function index(): View
    {
        $cart = session()->get('cart', []);
        $cartItems = [];
        $total = 0;

        foreach ($cart as $id => $details) {
            $product = Product::find($id);
            if ($product) {
                $cartItems[] = [
                    'id' => $id,
                    'product' => $product,
                    'quantity' => $details['quantity'],
                    'price' => $product->current_price,
                    'subtotal' => $product->current_price * $details['quantity']
                ];
                $total += $product->current_price * $details['quantity'];
            }
        }

        return view('frontend.cart.index', compact('cartItems', 'total'));
    }

    public function add(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1'
        ]);

        $product = Product::findOrFail($request->product_id);

        if (!$product->is_active || !$product->in_stock) {
            return response()->json(['message' => 'Product is not available'], 422);
        }

        $cart = session()->get('cart', []);
        $productId = $request->product_id;
        $quantity = $request->quantity;

        if (isset($cart[$productId])) {
            $cart[$productId]['quantity'] += $quantity;
        } else {
            $cart[$productId] = [
                'quantity' => $quantity,
                'price' => $product->current_price
            ];
        }

        session()->put('cart', $cart);

        $cartCount = array_sum(array_column($cart, 'quantity'));

        return response()->json([
            'message' => 'Product added to cart successfully',
            'cart_count' => $cartCount
        ]);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:1'
        ]);

        $cart = session()->get('cart', []);

        if (isset($cart[$id])) {
            $cart[$id]['quantity'] = $request->quantity;
            session()->put('cart', $cart);

            $product = Product::find($id);
            $subtotal = $product->current_price * $request->quantity;
            $total = $this->calculateCartTotal($cart);

            return response()->json([
                'message' => 'Cart updated successfully',
                'subtotal' => number_format($subtotal, 2),
                'total' => number_format($total, 2)
            ]);
        }

        return response()->json(['message' => 'Item not found in cart'], 404);
    }

    public function remove($id): JsonResponse
    {
        $cart = session()->get('cart', []);

        if (isset($cart[$id])) {
            unset($cart[$id]);
            session()->put('cart', $cart);

            $total = $this->calculateCartTotal($cart);
            $cartCount = array_sum(array_column($cart, 'quantity'));

            return response()->json([
                'message' => 'Item removed from cart',
                'total' => number_format($total, 2),
                'cart_count' => $cartCount
            ]);
        }

        return response()->json(['message' => 'Item not found in cart'], 404);
    }

    public function clear(): JsonResponse
    {
        session()->forget('cart');

        return response()->json(['message' => 'Cart cleared successfully']);
    }

    public function count(): JsonResponse
    {
        $cart = session()->get('cart', []);
        $count = array_sum(array_column($cart, 'quantity'));

        return response()->json(['count' => $count]);
    }

    private function calculateCartTotal(array $cart): float
    {
        $total = 0;
        foreach ($cart as $id => $details) {
            $product = Product::find($id);
            if ($product) {
                $total += $product->current_price * $details['quantity'];
            }
        }
        return $total;
    }
}
