@extends('admin.layout')

@section('title', 'Category Details')
@section('page-title', 'Category: ' . $category->name)

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Category Info -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Category Information</h2>
            <div class="flex items-center space-x-2">
                <a href="{{ route('admin.categories.edit', $category) }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                    <i class="fas fa-edit mr-2"></i>Edit
                </a>
                @if($category->products->count() == 0 && $category->children->count() == 0)
                    <form action="{{ route('admin.categories.destroy', $category) }}" 
                          method="POST" 
                          class="inline"
                          onsubmit="return confirm('Are you sure you want to delete this category?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition duration-300">
                            <i class="fas fa-trash mr-2"></i>Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Name</h3>
                <p class="text-lg text-gray-900">{{ $category->name }}</p>
            </div>

            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Slug</h3>
                <p class="text-lg text-gray-900">{{ $category->slug }}</p>
            </div>

            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Parent Category</h3>
                <p class="text-lg text-gray-900">
                    @if($category->parent)
                        <a href="{{ route('admin.categories.show', $category->parent) }}" 
                           class="text-blue-600 hover:text-blue-800">
                            {{ $category->parent->name }}
                        </a>
                    @else
                        Top Level Category
                    @endif
                </p>
            </div>

            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Status</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $category->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                    {{ $category->is_active ? 'Active' : 'Inactive' }}
                </span>
            </div>

            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Sort Order</h3>
                <p class="text-lg text-gray-900">{{ $category->sort_order }}</p>
            </div>

            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-1">Products Count</h3>
                <p class="text-lg text-gray-900">{{ $category->products->count() }}</p>
            </div>
        </div>

        @if($category->description)
            <div class="mt-6">
                <h3 class="text-sm font-medium text-gray-500 mb-1">Description</h3>
                <p class="text-gray-900">{{ $category->description }}</p>
            </div>
        @endif
    </div>

    <!-- Subcategories -->
    @if($category->children->count() > 0)
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Subcategories</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($category->children as $child)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-gray-900">{{ $child->name }}</h4>
                                <p class="text-sm text-gray-500">{{ $child->products_count ?? 0 }} products</p>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $child->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $child->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        <div class="mt-2">
                            <a href="{{ route('admin.categories.show', $child) }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View Details →
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Products -->
    @if($category->products->count() > 0)
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Products in this Category</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Product
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Price
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Stock
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($category->products as $product)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        @if($product->main_image)
                                            <img src="{{ $product->main_image }}" 
                                                 alt="{{ $product->name }}" 
                                                 class="w-10 h-10 object-cover rounded mr-3">
                                        @else
                                            <div class="w-10 h-10 bg-gray-200 rounded mr-3 flex items-center justify-center">
                                                <i class="fas fa-image text-gray-400"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $product->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $product->sku }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    Rp {{ number_format($product->price, 0, ',', '.') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $product->stock_quantity }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $product->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.products.show', $product) }}" 
                                       class="text-blue-600 hover:text-blue-900">
                                        View
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
</div>
@endsection
