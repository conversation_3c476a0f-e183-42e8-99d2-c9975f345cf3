@extends('admin.layout')

@section('title', $product->name)
@section('page-title', 'Product Details')

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.products.index') }}" 
                       class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Products
                    </a>
                    <h3 class="text-lg font-medium text-gray-900">{{ $product->name }}</h3>
                </div>
                <div class="flex items-center space-x-2">
                    <a href="{{ route('admin.products.edit', $product) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                        <i class="fas fa-edit mr-2"></i>Edit Product
                    </a>
                    <form method="POST" action="{{ route('admin.products.toggle-status', $product) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" 
                                class="bg-{{ $product->is_active ? 'yellow' : 'green' }}-600 text-white px-4 py-2 rounded-md hover:bg-{{ $product->is_active ? 'yellow' : 'green' }}-700 transition duration-300">
                            <i class="fas fa-{{ $product->is_active ? 'eye-slash' : 'eye' }} mr-2"></i>
                            {{ $product->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Product Images -->
            @if($product->images && count($product->images) > 0)
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-md font-medium text-gray-900">Product Images</h4>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                            @foreach($product->images as $index => $image)
                                <div class="relative">
                                    <img src="{{ $image }}" 
                                         alt="{{ $product->name }} - Image {{ $index + 1 }}" 
                                         class="w-full h-32 object-cover rounded-lg border">
                                    @if($index === 0)
                                        <span class="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                                            Main
                                        </span>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Product Description -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h4 class="text-md font-medium text-gray-900">Description</h4>
                </div>
                <div class="p-6">
                    @if($product->short_description)
                        <div class="mb-4">
                            <h5 class="text-sm font-medium text-gray-700 mb-2">Short Description</h5>
                            <p class="text-gray-600">{{ $product->short_description }}</p>
                        </div>
                    @endif
                    
                    <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-2">Full Description</h5>
                        <div class="text-gray-600 whitespace-pre-line">{{ $product->description }}</div>
                    </div>
                </div>
            </div>

            <!-- Product Attributes -->
            @if($product->attributes && count($product->attributes) > 0)
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-md font-medium text-gray-900">Product Attributes</h4>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($product->attributes as $key => $value)
                                <div class="flex justify-between py-2 border-b border-gray-100">
                                    <span class="font-medium text-gray-700">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                    <span class="text-gray-600">{{ $value }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Product Info -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h4 class="text-md font-medium text-gray-900">Product Information</h4>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">SKU</label>
                        <p class="text-gray-900">{{ $product->sku }}</p>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-gray-500">Category</label>
                        <p class="text-gray-900">{{ $product->category->name ?? 'No Category' }}</p>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-gray-500">Status</label>
                        <div class="flex flex-col space-y-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium w-fit {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            @if($product->is_featured)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 w-fit">
                                    Featured
                                </span>
                            @endif
                        </div>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-gray-500">Created</label>
                        <p class="text-gray-900">{{ $product->created_at->format('M d, Y') }}</p>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-gray-500">Last Updated</label>
                        <p class="text-gray-900">{{ $product->updated_at->format('M d, Y') }}</p>
                    </div>
                </div>
            </div>

            <!-- Pricing -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h4 class="text-md font-medium text-gray-900">Pricing</h4>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Regular Price</label>
                        <p class="text-2xl font-bold text-gray-900">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                    </div>

                    @if($product->sale_price)
                        <div>
                            <label class="text-sm font-medium text-gray-500">Sale Price</label>
                            <p class="text-2xl font-bold text-red-600">Rp {{ number_format($product->sale_price, 0, ',', '.') }}</p>
                            <p class="text-sm text-green-600">
                                Save Rp {{ number_format($product->price - $product->sale_price, 0, ',', '.') }}
                                ({{ round((($product->price - $product->sale_price) / $product->price) * 100) }}% off)
                            </p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Inventory -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h4 class="text-md font-medium text-gray-900">Inventory</h4>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Stock Quantity</label>
                        <p class="text-2xl font-bold {{ $product->stock_quantity <= 5 ? 'text-red-600' : 'text-gray-900' }}">
                            {{ $product->stock_quantity }}
                        </p>
                        @if($product->stock_quantity <= 5)
                            <p class="text-sm text-red-600">⚠️ Low stock alert</p>
                        @endif
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-gray-500">Stock Management</label>
                        <p class="text-gray-900">{{ $product->manage_stock ? 'Enabled' : 'Disabled' }}</p>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-gray-500">Stock Status</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $product->in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $product->in_stock ? 'In Stock' : 'Out of Stock' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Physical Properties -->
            @if($product->weight || $product->dimensions)
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h4 class="text-md font-medium text-gray-900">Physical Properties</h4>
                    </div>
                    <div class="p-6 space-y-4">
                        @if($product->weight)
                            <div>
                                <label class="text-sm font-medium text-gray-500">Weight</label>
                                <p class="text-gray-900">{{ $product->weight }} kg</p>
                            </div>
                        @endif
                        
                        @if($product->dimensions)
                            <div>
                                <label class="text-sm font-medium text-gray-500">Dimensions</label>
                                <p class="text-gray-900">{{ $product->dimensions }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h4 class="text-md font-medium text-gray-900">Quick Actions</h4>
                </div>
                <div class="p-6 space-y-3">
                    <form method="POST" action="{{ route('admin.products.toggle-featured', $product) }}" class="w-full">
                        @csrf
                        @method('PATCH')
                        <button type="submit" 
                                class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                            <i class="fas fa-star mr-2"></i>
                            {{ $product->is_featured ? 'Remove from Featured' : 'Mark as Featured' }}
                        </button>
                    </form>
                    
                    <a href="{{ route('products.show', $product->slug) }}" 
                       target="_blank"
                       class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-300 text-center block">
                        <i class="fas fa-external-link-alt mr-2"></i>View on Store
                    </a>
                    
                    <form method="POST" action="{{ route('admin.products.destroy', $product) }}" 
                          class="w-full" 
                          onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition duration-300">
                            <i class="fas fa-trash mr-2"></i>Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
