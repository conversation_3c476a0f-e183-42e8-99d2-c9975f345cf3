@extends('frontend.layout')

@section('title', 'Order #' . $order->order_number . ' - E-Store')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
            <li><a href="{{ route('account.dashboard') }}" class="hover:text-blue-600">Account</a></li>
            <li><i class="fas fa-chevron-right text-xs"></i></li>
            <li><a href="{{ route('account.orders') }}" class="hover:text-blue-600">Orders</a></li>
            <li><i class="fas fa-chevron-right text-xs"></i></li>
            <li class="text-gray-800">Order #{{ $order->order_number }}</li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Order Details -->
        <div class="lg:col-span-2">
            <!-- Order Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">Order #{{ $order->order_number }}</h1>
                        <p class="text-gray-600">Placed on {{ $order->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    <span class="mt-2 sm:mt-0 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                        @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                        @elseif($order->status === 'shipped') bg-purple-100 text-purple-800
                        @elseif($order->status === 'completed') bg-green-100 text-green-800
                        @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                        @else bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst($order->status) }}
                    </span>
                </div>

                <!-- Order Progress -->
                <div class="mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center {{ $order->status === 'pending' || $order->status === 'processing' || $order->status === 'shipped' || $order->status === 'completed' ? 'text-blue-600' : 'text-gray-400' }}">
                            <div class="w-8 h-8 rounded-full {{ $order->status === 'pending' || $order->status === 'processing' || $order->status === 'shipped' || $order->status === 'completed' ? 'bg-blue-600' : 'bg-gray-300' }} flex items-center justify-center">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium">Order Placed</span>
                        </div>
                        
                        <div class="flex items-center {{ $order->status === 'processing' || $order->status === 'shipped' || $order->status === 'completed' ? 'text-blue-600' : 'text-gray-400' }}">
                            <div class="w-8 h-8 rounded-full {{ $order->status === 'processing' || $order->status === 'shipped' || $order->status === 'completed' ? 'bg-blue-600' : 'bg-gray-300' }} flex items-center justify-center">
                                <i class="fas fa-cog text-white text-sm"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium">Processing</span>
                        </div>
                        
                        <div class="flex items-center {{ $order->status === 'shipped' || $order->status === 'completed' ? 'text-blue-600' : 'text-gray-400' }}">
                            <div class="w-8 h-8 rounded-full {{ $order->status === 'shipped' || $order->status === 'completed' ? 'bg-blue-600' : 'bg-gray-300' }} flex items-center justify-center">
                                <i class="fas fa-truck text-white text-sm"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium">Shipped</span>
                        </div>
                        
                        <div class="flex items-center {{ $order->status === 'completed' ? 'text-green-600' : 'text-gray-400' }}">
                            <div class="w-8 h-8 rounded-full {{ $order->status === 'completed' ? 'bg-green-600' : 'bg-gray-300' }} flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium">Delivered</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">Order Items</h2>
                </div>
                <div class="divide-y divide-gray-200">
                    @foreach($order->orderItems as $item)
                        <div class="p-6">
                            <div class="flex items-center space-x-4">
                                @if($item->product && $item->product->main_image)
                                    <img src="{{ $item->product->main_image }}" 
                                         alt="{{ $item->product->name }}" 
                                         class="w-20 h-20 object-cover rounded-md">
                                @else
                                    <div class="w-20 h-20 bg-gray-200 rounded-md flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                @endif
                                
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-800">
                                        @if($item->product)
                                            <a href="{{ route('products.show', $item->product->slug) }}" 
                                               class="hover:text-blue-600">
                                                {{ $item->product->name }}
                                            </a>
                                        @else
                                            {{ $item->product_name ?? 'Product not found' }}
                                        @endif
                                    </h3>
                                    @if($item->product && $item->product->category)
                                        <p class="text-sm text-gray-600">{{ $item->product->category->name }}</p>
                                    @endif
                                    <p class="text-sm text-gray-600 mt-1">
                                        Price: Rp {{ number_format($item->price, 0, ',', '.') }} × {{ $item->quantity }}
                                    </p>
                                </div>
                                
                                <div class="text-right">
                                    <p class="text-lg font-bold text-gray-800">
                                        Rp {{ number_format($item->quantity * $item->price, 0, ',', '.') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Order Summary & Info -->
        <div class="lg:col-span-1">
            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Order Summary</h2>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="font-semibold">Rp {{ number_format($order->subtotal ?? ($order->total_amount - $order->tax_amount - $order->shipping_amount), 0, ',', '.') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Shipping</span>
                        <span class="font-semibold">Rp {{ number_format($order->shipping_amount ?? 0, 0, ',', '.') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Tax</span>
                        <span class="font-semibold">Rp {{ number_format($order->tax_amount ?? 0, 0, ',', '.') }}</span>
                    </div>
                    <div class="border-t pt-2">
                        <div class="flex justify-between">
                            <span class="text-lg font-semibold">Total</span>
                            <span class="text-lg font-bold text-blue-600">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shipping Information -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Shipping Information</h2>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-800">{{ $order->customer_name }}</p>
                    <p>{{ $order->shipping_address }}</p>
                    <p>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}</p>
                    @if($order->shipping_country)
                        <p>{{ $order->shipping_country }}</p>
                    @endif
                </div>
            </div>

            <!-- Billing Information -->
            @if($order->billing_address)
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Billing Information</h2>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p class="font-medium text-gray-800">{{ $order->customer_name }}</p>
                        <p>{{ $order->billing_address }}</p>
                        <p>{{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_postal_code }}</p>
                        @if($order->billing_country)
                            <p>{{ $order->billing_country }}</p>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Order Notes -->
            @if($order->notes)
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Order Notes</h2>
                    <p class="text-sm text-gray-600">{{ $order->notes }}</p>
                </div>
            @endif

            <!-- Actions -->
            <div class="mt-6 space-y-3">
                @if($order->status === 'pending')
                    <button class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition duration-300">
                        Cancel Order
                    </button>
                @endif
                
                <a href="{{ route('account.orders') }}" 
                   class="block w-full text-center bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition duration-300">
                    Back to Orders
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
