@extends('frontend.layout')

@section('title', 'Selamat Datang di Sahabat Rumah')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-navy-blue to-blue-800 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                <i class="fas fa-home text-pastel-orange mr-4"></i>
                Selamat Datang di Sahabat Rumah
            </h1>
            <p class="text-xl md:text-2xl mb-8">Temukan produk rumah tangga berkualitas dengan harga terjangkau</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('products.index') }}" class="bg-pastel-orange text-navy-blue px-8 py-3 rounded-lg text-lg font-semibold hover:bg-orange-300 transition duration-300">
                    <i class="fas fa-shopping-bag mr-2"></i>
                    Belanja Sekarang
                </a>
                <a href="#featured" class="border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-navy-blue transition duration-300">
                    <i class="fas fa-star mr-2"></i>
                    Produk Unggulan
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-16 bg-light-orange">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-4 text-navy-blue">Belanja Berdasarkan Kategori</h2>
        <p class="text-center text-gray-600 mb-12">Pilih kategori produk yang Anda butuhkan</p>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            @foreach($categories as $category)
                <a href="{{ route('categories.show', $category->slug) }}"
                   class="group bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 p-6 text-center border-2 border-transparent hover:border-pastel-orange">
                    @if($category->image)
                        <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-16 h-16 mx-auto mb-4 object-cover rounded">
                    @else
                        <div class="w-16 h-16 mx-auto mb-4 bg-pastel-orange bg-opacity-20 rounded flex items-center justify-center">
                            <i class="fas fa-tag text-pastel-orange text-2xl"></i>
                        </div>
                    @endif
                    <h3 class="font-semibold text-navy-blue group-hover:text-pastel-orange transition duration-300">{{ $category->name }}</h3>
                    <p class="text-sm text-gray-600 mt-1">{{ $category->products_count ?? 0 }} produk</p>
                </a>
            @endforeach
        </div>
    </div>
</section>

<!-- Featured Products -->
<section id="featured" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-4 text-navy-blue">Produk Unggulan</h2>
        <p class="text-center text-gray-600 mb-12">Produk terbaik pilihan untuk rumah Anda</p>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($featuredProducts as $product)
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 overflow-hidden border-2 border-transparent hover:border-pastel-orange">
                    <a href="{{ route('products.show', $product->slug) }}">
                        @if($product->main_image)
                            <img src="{{ $product->main_image }}" alt="{{ $product->name }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-light-orange flex items-center justify-center">
                                <i class="fas fa-image text-pastel-orange text-4xl"></i>
                            </div>
                        @endif
                    </a>
                    <div class="p-4">
                        <a href="{{ route('products.show', $product->slug) }}" class="block">
                            <h3 class="font-semibold text-navy-blue hover:text-pastel-orange mb-2 transition duration-300">{{ $product->name }}</h3>
                        </a>
                        <p class="text-gray-600 text-sm mb-3">{{ Str::limit($product->short_description, 80) }}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                @if($product->sale_price)
                                    <span class="text-lg font-bold text-red-600">{{ $product->formatted_current_price }}</span>
                                    <span class="text-sm text-gray-500 line-through">{{ $product->formatted_price }}</span>
                                @else
                                    <span class="text-lg font-bold text-navy-blue">{{ $product->formatted_price }}</span>
                                @endif
                            </div>
                            <button onclick="addToCart({{ $product->id }})"
                                    class="bg-pastel-orange text-navy-blue px-3 py-1 rounded hover:bg-orange-300 transition duration-300 font-medium">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="text-center mt-8">
            <a href="{{ route('products.index') }}" class="bg-navy-blue text-white px-6 py-3 rounded-lg hover:bg-blue-800 transition duration-300">
                <i class="fas fa-eye mr-2"></i>
                Lihat Semua Produk
            </a>
        </div>
    </div>
</section>

<!-- New Products -->
<section class="py-16 bg-light-orange">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-4 text-navy-blue">Produk Terbaru</h2>
        <p class="text-center text-gray-600 mb-12">Koleksi terbaru untuk melengkapi rumah Anda</p>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($newProducts as $product)
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 overflow-hidden border-2 border-transparent hover:border-pastel-orange">
                    <div class="relative">
                        <a href="{{ route('products.show', $product->slug) }}">
                            @if($product->main_image)
                                <img src="{{ $product->main_image }}" alt="{{ $product->name }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-light-orange flex items-center justify-center">
                                    <i class="fas fa-image text-pastel-orange text-4xl"></i>
                                </div>
                            @endif
                        </a>
                        <span class="absolute top-2 left-2 bg-pastel-orange text-navy-blue px-2 py-1 text-xs font-bold rounded">BARU</span>
                    </div>
                    <div class="p-4">
                        <a href="{{ route('products.show', $product->slug) }}" class="block">
                            <h3 class="font-semibold text-navy-blue hover:text-pastel-orange mb-2 transition duration-300">{{ $product->name }}</h3>
                        </a>
                        <p class="text-gray-600 text-sm mb-3">{{ Str::limit($product->short_description, 80) }}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                @if($product->sale_price)
                                    <span class="text-lg font-bold text-red-600">{{ $product->formatted_current_price }}</span>
                                    <span class="text-sm text-gray-500 line-through">{{ $product->formatted_price }}</span>
                                @else
                                    <span class="text-lg font-bold text-navy-blue">{{ $product->formatted_price }}</span>
                                @endif
                            </div>
                            <button onclick="addToCart({{ $product->id }})"
                                    class="bg-pastel-orange text-navy-blue px-3 py-1 rounded hover:bg-orange-300 transition duration-300 font-medium">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-4 text-navy-blue">Mengapa Memilih Sahabat Rumah?</h2>
        <p class="text-center text-gray-600 mb-12">Komitmen kami untuk memberikan pelayanan terbaik</p>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="bg-pastel-orange text-navy-blue w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shipping-fast text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 text-navy-blue">Gratis Ongkir</h3>
                <p class="text-gray-600">Gratis ongkos kirim untuk pembelian di atas Rp 500.000</p>
            </div>
            <div class="text-center">
                <div class="bg-pastel-orange text-navy-blue w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-undo text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 text-navy-blue">Mudah Dikembalikan</h3>
                <p class="text-gray-600">Kebijakan pengembalian 30 hari</p>
            </div>
            <div class="text-center">
                <div class="bg-pastel-orange text-navy-blue w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-headset text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2 text-navy-blue">Layanan 24/7</h3>
                <p class="text-gray-600">Dukungan pelanggan tersedia kapan saja</p>
            </div>
        </div>
    </div>
</section>

<script>
async function addToCart(productId) {
    const result = await window.cartAPI.add(productId, 1);
    
    if (result.success) {
        // Update cart count in navigation
        const cartCountElement = document.querySelector('[x-text="cartCount"]');
        if (cartCountElement) {
            cartCountElement.__x.$data.cartCount++;
        }
        
        // Show success message
        showNotification(result.message, 'success');
    } else {
        showNotification(result.message, 'error');
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
