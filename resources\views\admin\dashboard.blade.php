@extends('admin.layout')

@section('title', 'Dashboard')
@section('page-title', 'Admin Dashboard')

@section('content')
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Products -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-box text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Products</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $totalProducts }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{{ route('admin.products.index') }}" class="text-sm text-blue-600 hover:text-blue-800">
                View all products →
            </a>
        </div>
    </div>

    <!-- Total Categories -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-tags text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Categories</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $totalCategories }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{{ route('admin.categories.index') }}" class="text-sm text-green-600 hover:text-green-800">
                Manage categories →
            </a>
        </div>
    </div>

    <!-- Total Orders -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Orders</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $totalOrders }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{{ route('admin.orders.index') }}" class="text-sm text-purple-600 hover:text-purple-800">
                View all orders →
            </a>
        </div>
    </div>

    <!-- Low Stock Alert -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Low Stock Items</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $lowStockProducts }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{{ route('admin.products.index') }}?filter=low_stock" class="text-sm text-red-600 hover:text-red-800">
                Check inventory →
            </a>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Quick Actions Card -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-2 gap-4">
                <a href="{{ route('admin.products.create') }}" 
                   class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition duration-300">
                    <div class="text-center">
                        <i class="fas fa-plus text-2xl text-gray-400 mb-2"></i>
                        <p class="text-sm font-medium text-gray-900">Add Product</p>
                    </div>
                </a>
                
                <a href="{{ route('admin.categories.create') }}" 
                   class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition duration-300">
                    <div class="text-center">
                        <i class="fas fa-tag text-2xl text-gray-400 mb-2"></i>
                        <p class="text-sm font-medium text-gray-900">Add Category</p>
                    </div>
                </a>
                
                <a href="{{ route('admin.orders.index') }}" 
                   class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition duration-300">
                    <div class="text-center">
                        <i class="fas fa-list text-2xl text-gray-400 mb-2"></i>
                        <p class="text-sm font-medium text-gray-900">View Orders</p>
                    </div>
                </a>
                
                <a href="{{ route('home') }}" 
                   class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-500 hover:bg-indigo-50 transition duration-300">
                    <div class="text-center">
                        <i class="fas fa-external-link-alt text-2xl text-gray-400 mb-2"></i>
                        <p class="text-sm font-medium text-gray-900">View Store</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @php
                    $recentProducts = \App\Models\Product::latest()->take(5)->get();
                @endphp
                
                @forelse($recentProducts as $product)
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            @if($product->main_image)
                                <img src="{{ $product->main_image }}" 
                                     alt="{{ $product->name }}" 
                                     class="w-8 h-8 object-cover rounded">
                            @else
                                <div class="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                </div>
                            @endif
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                                {{ $product->name }}
                            </p>
                            <p class="text-xs text-gray-500">
                                Added {{ $product->created_at->diffForHumans() }}
                            </p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                @empty
                    <div class="text-center text-gray-500">
                        <i class="fas fa-inbox text-2xl mb-2"></i>
                        <p class="text-sm">No recent activity</p>
                    </div>
                @endforelse
            </div>
            
            @if($recentProducts->count() > 0)
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <a href="{{ route('admin.products.index') }}" 
                       class="text-sm text-blue-600 hover:text-blue-800">
                        View all products →
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Welcome Message -->
<div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg text-white p-6">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-xl font-bold mb-2">Welcome to E-Store Admin!</h2>
            <p class="text-blue-100">
                Manage your products, categories, and orders from this dashboard. 
                Get started by adding your first product or exploring the existing inventory.
            </p>
        </div>
        <div class="hidden md:block">
            <i class="fas fa-store text-4xl text-blue-200"></i>
        </div>
    </div>
    
    <div class="mt-4 flex space-x-4">
        <a href="{{ route('admin.products.create') }}" 
           class="bg-white text-blue-600 px-4 py-2 rounded-md hover:bg-blue-50 transition duration-300 text-sm font-medium">
            <i class="fas fa-plus mr-2"></i>Add Your First Product
        </a>
        <a href="{{ route('home') }}" 
           class="border border-white text-white px-4 py-2 rounded-md hover:bg-white hover:text-blue-600 transition duration-300 text-sm font-medium">
            <i class="fas fa-external-link-alt mr-2"></i>View Your Store
        </a>
    </div>
</div>
@endsection
