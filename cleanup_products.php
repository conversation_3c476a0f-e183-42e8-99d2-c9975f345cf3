<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Product;
use App\Models\Category;

echo "🧹 Membersihkan produk yang tidak masuk kategori Electronics dan Clothing...\n\n";

try {
    // Cari kategori Electronics dan Clothing
    $electronics = Category::where('name', 'LIKE', '%Electronics%')->first();
    $clothing = Category::where('name', 'LIKE', '%Clothing%')->first();
    
    if (!$electronics) {
        echo "❌ Kategori Electronics tidak ditemukan!\n";
        exit;
    }
    
    if (!$clothing) {
        echo "❌ Kategori Clothing tidak ditemukan!\n";
        exit;
    }
    
    echo "📂 Kategori Electronics ID: {$electronics->id} - {$electronics->name}\n";
    echo "📂 Kategori Clothing ID: {$clothing->id} - {$clothing->name}\n\n";
    
    // Ambil semua produk
    $allProducts = Product::with('category')->get();
    
    echo "📊 Total produk saat ini: " . $allProducts->count() . "\n\n";
    
    // Identifikasi produk yang akan dihapus
    $productsToDelete = $allProducts->filter(function ($product) use ($electronics, $clothing) {
        return $product->category_id !== $electronics->id && $product->category_id !== $clothing->id;
    });
    
    if ($productsToDelete->isEmpty()) {
        echo "✅ Semua produk sudah berada dalam kategori Electronics atau Clothing!\n";
        echo "📊 Total produk Electronics: " . Product::where('category_id', $electronics->id)->count() . "\n";
        echo "📊 Total produk Clothing: " . Product::where('category_id', $clothing->id)->count() . "\n";
        exit;
    }
    
    echo "🗑️  Produk yang akan dihapus (" . $productsToDelete->count() . " produk):\n\n";
    
    foreach ($productsToDelete as $product) {
        $categoryName = $product->category ? $product->category->name : 'Tidak ada kategori';
        $price = 'Rp ' . number_format($product->price, 0, ',', '.');
        
        echo "❌ {$product->name}\n";
        echo "   SKU: {$product->sku} | Kategori: {$categoryName} | Harga: {$price}\n";
        
        // Cek apakah produk memiliki order items
        $orderItemsCount = $product->orderItems()->count();
        if ($orderItemsCount > 0) {
            echo "   ⚠️  PERINGATAN: Produk ini memiliki {$orderItemsCount} order item(s)!\n";
        }
        echo "\n";
    }
    
    // Konfirmasi penghapusan
    echo "⚠️  PERINGATAN: Operasi ini akan menghapus " . $productsToDelete->count() . " produk!\n";
    echo "📋 Produk yang akan TETAP ADA:\n";
    echo "   - Electronics: " . Product::where('category_id', $electronics->id)->count() . " produk\n";
    echo "   - Clothing: " . Product::where('category_id', $clothing->id)->count() . " produk\n\n";
    
    // Proses penghapusan
    $deletedCount = 0;
    $skippedCount = 0;
    
    foreach ($productsToDelete as $product) {
        try {
            // Cek order items lagi sebelum menghapus
            $orderItemsCount = $product->orderItems()->count();
            
            if ($orderItemsCount > 0) {
                echo "⏭️  Melewati {$product->name} (memiliki {$orderItemsCount} order item)\n";
                $skippedCount++;
                continue;
            }
            
            // Hapus produk
            $productName = $product->name;
            $product->delete();
            
            echo "🗑️  Berhasil menghapus: {$productName}\n";
            $deletedCount++;
            
        } catch (Exception $e) {
            echo "❌ Error menghapus {$product->name}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🎉 Proses pembersihan selesai!\n";
    echo "✅ Berhasil menghapus: {$deletedCount} produk\n";
    echo "⏭️  Dilewati (ada order): {$skippedCount} produk\n";
    
    // Show final stats
    $finalTotal = Product::count();
    $finalElectronics = Product::where('category_id', $electronics->id)->count();
    $finalClothing = Product::where('category_id', $clothing->id)->count();
    
    echo "\n📊 STATISTIK AKHIR:\n";
    echo "📱 Total produk Electronics: {$finalElectronics}\n";
    echo "👕 Total produk Clothing: {$finalClothing}\n";
    echo "📦 Total produk keseluruhan: {$finalTotal}\n";
    
    if ($finalTotal !== ($finalElectronics + $finalClothing)) {
        $remaining = $finalTotal - ($finalElectronics + $finalClothing);
        echo "⚠️  Masih ada {$remaining} produk di kategori lain\n";
    } else {
        echo "✅ Semua produk sekarang hanya dalam kategori Electronics dan Clothing!\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🏁 Selesai!\n";
