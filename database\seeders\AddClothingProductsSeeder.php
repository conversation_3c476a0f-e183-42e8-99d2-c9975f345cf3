<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class AddClothingProductsSeeder extends Seeder
{
    /**
     * Add clothing products to the database
     */
    public function run(): void
    {
        $this->command->info('👕 Menambahkan produk clothing ke toko Sahabat Rumah...');
        $this->command->info('');

        // Cari atau buat kategori Clothing
        $clothingCategory = Category::firstOrCreate(
            ['name' => 'Clothing'],
            [
                'description' => 'Koleksi pakaian fashion terkini untuk pria dan wanita',
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        $this->command->info("📂 Kategori: {$clothingCategory->name} (ID: {$clothingCategory->id})");
        $this->command->info('');

        // Data produk clothing
        $clothingProducts = [
            [
                'name' => 'Kemeja Formal Pria Putih',
                'slug' => 'kemeja-formal-pria-putih',
                'sku' => 'SHIRT-FORMAL-WHITE-001',
                'description' => 'Kemeja formal pria warna putih, bahan katun premium, cocok untuk acara formal dan kantor. Tersedia ukuran S, M, L, XL.',
                'price' => 285000,
                'sale_price' => 250000,
                'stock_quantity' => 50,
                'images' => ['https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Katun Premium',
                    'Warna' => 'Putih',
                    'Ukuran' => 'S, M, L, XL',
                    'Kerah' => 'Kerah Kancing',
                    'Perawatan' => 'Cuci dengan air dingin'
                ])
            ],
            [
                'name' => 'Dress Casual Wanita Navy',
                'slug' => 'dress-casual-wanita-navy',
                'sku' => 'DRESS-CASUAL-NAVY-001',
                'description' => 'Dress casual wanita warna navy blue, model A-line yang nyaman untuk aktivitas sehari-hari. Bahan rayon berkualitas tinggi.',
                'price' => 320000,
                'sale_price' => 280000,
                'stock_quantity' => 35,
                'images' => ['https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Rayon Premium',
                    'Warna' => 'Navy Blue',
                    'Ukuran' => 'S, M, L, XL',
                    'Model' => 'A-Line',
                    'Panjang' => 'Midi'
                ])
            ],
            [
                'name' => 'Jaket Denim Pria Classic',
                'slug' => 'jaket-denim-pria-classic',
                'sku' => 'JACKET-DENIM-CLASSIC-001',
                'description' => 'Jaket denim pria model classic, warna biru tua dengan detail vintage. Cocok untuk gaya kasual dan semi-formal.',
                'price' => 450000,
                'sale_price' => 395000,
                'stock_quantity' => 25,
                'images' => ['https://images.unsplash.com/photo-1551028719-00167b16eac5?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Denim 100%',
                    'Warna' => 'Dark Blue',
                    'Ukuran' => 'M, L, XL, XXL',
                    'Style' => 'Classic Vintage',
                    'Kantong' => '4 Kantong'
                ])
            ],
            [
                'name' => 'Blouse Wanita Floral Print',
                'slug' => 'blouse-wanita-floral-print',
                'sku' => 'BLOUSE-FLORAL-PRINT-001',
                'description' => 'Blouse wanita dengan motif floral print yang elegan, bahan chiffon ringan dan adem. Perfect untuk acara semi-formal.',
                'price' => 195000,
                'sale_price' => 165000,
                'stock_quantity' => 40,
                'images' => ['https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Chiffon',
                    'Pattern' => 'Floral Print',
                    'Ukuran' => 'S, M, L',
                    'Lengan' => 'Lengan Panjang',
                    'Kerah' => 'V-Neck'
                ])
            ],
            [
                'name' => 'Celana Chino Pria Khaki',
                'slug' => 'celana-chino-pria-khaki',
                'sku' => 'CHINO-KHAKI-001',
                'description' => 'Celana chino pria warna khaki, model slim fit yang nyaman untuk aktivitas sehari-hari. Bahan twill berkualitas tinggi.',
                'price' => 275000,
                'sale_price' => 235000,
                'stock_quantity' => 45,
                'images' => ['https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Cotton Twill',
                    'Warna' => 'Khaki',
                    'Ukuran' => '28, 30, 32, 34, 36',
                    'Fit' => 'Slim Fit',
                    'Kantong' => '5 Kantong'
                ])
            ],
            [
                'name' => 'Sweater Rajut Wanita Cream',
                'slug' => 'sweater-rajut-wanita-cream',
                'sku' => 'SWEATER-KNIT-CREAM-001',
                'description' => 'Sweater rajut wanita warna cream, model oversized yang trendy dan hangat. Cocok untuk cuaca dingin atau ber-AC.',
                'price' => 385000,
                'sale_price' => 325000,
                'stock_quantity' => 30,
                'images' => ['https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Wool Blend',
                    'Warna' => 'Cream',
                    'Ukuran' => 'S, M, L',
                    'Model' => 'Oversized',
                    'Kerah' => 'Round Neck'
                ])
            ],
            [
                'name' => 'Polo Shirt Pria Navy',
                'slug' => 'polo-shirt-pria-navy',
                'sku' => 'POLO-NAVY-001',
                'description' => 'Polo shirt pria warna navy, bahan pique cotton yang nyaman dan breathable. Cocok untuk olahraga dan kasual.',
                'price' => 165000,
                'sale_price' => 145000,
                'stock_quantity' => 60,
                'images' => ['https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Pique Cotton',
                    'Warna' => 'Navy Blue',
                    'Ukuran' => 'S, M, L, XL, XXL',
                    'Kerah' => 'Polo Collar',
                    'Kancing' => '3 Kancing'
                ])
            ],
            [
                'name' => 'Rok Midi Wanita Hitam',
                'slug' => 'rok-midi-wanita-hitam',
                'sku' => 'SKIRT-MIDI-BLACK-001',
                'description' => 'Rok midi wanita warna hitam, model A-line yang elegan untuk acara formal maupun kasual. Bahan polyester premium.',
                'price' => 225000,
                'sale_price' => 195000,
                'stock_quantity' => 35,
                'images' => ['https://images.unsplash.com/photo-1583496661160-fb5886a13d77?w=500&h=500&fit=crop&crop=center'],
                'attributes' => json_encode([
                    'Material' => 'Polyester Premium',
                    'Warna' => 'Hitam',
                    'Ukuran' => 'S, M, L, XL',
                    'Panjang' => 'Midi (70cm)',
                    'Model' => 'A-Line'
                ])
            ]
        ];

        $addedCount = 0;

        foreach ($clothingProducts as $productData) {
            try {
                // Cek apakah produk sudah ada berdasarkan SKU
                $existingProduct = Product::where('sku', $productData['sku'])->first();
                
                if ($existingProduct) {
                    $this->command->warn("⚠️  Produk {$productData['name']} sudah ada (SKU: {$productData['sku']})");
                    continue;
                }

                $product = Product::create([
                    'name' => $productData['name'],
                    'sku' => $productData['sku'],
                    'description' => $productData['description'],
                    'price' => $productData['price'],
                    'sale_price' => $productData['sale_price'],
                    'stock' => $productData['stock'],
                    'category_id' => $clothingCategory->id,
                    'images' => $productData['images'],
                    'attributes' => $productData['attributes'],
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                $price = 'Rp ' . number_format($product->price, 0, ',', '.');
                $salePrice = 'Rp ' . number_format($product->sale_price, 0, ',', '.');
                
                $this->command->info("✅ {$product->name}");
                $this->command->line("   SKU: {$product->sku} | Harga: {$price} (diskon: {$salePrice}) | Stok: {$product->stock}");
                
                $addedCount++;
                
            } catch (\Exception $e) {
                $this->command->error("❌ Error menambahkan {$productData['name']}: " . $e->getMessage());
            }
        }

        $this->command->info('');
        $this->command->info("🎉 Berhasil menambahkan {$addedCount} produk clothing!");
        
        // Show total products
        $totalProducts = Product::count();
        $clothingProducts = Product::where('category_id', $clothingCategory->id)->count();
        
        $this->command->info("📊 Total produk di toko: {$totalProducts}");
        $this->command->info("👕 Total produk clothing: {$clothingProducts}");
    }
}
