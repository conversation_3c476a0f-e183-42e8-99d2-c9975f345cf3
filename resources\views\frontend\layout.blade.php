<!DOCTYPE html>
<html lang="en" x-data="{ cartCount: {{ session()->has('cart') ? array_sum(array_column(session('cart'), 'quantity')) : 0 }} }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Sahabat Rumah')</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pastel-orange': '#FFB366',
                        'navy-blue': '#1E3A8A',
                        'light-orange': '#FFF4E6',
                    }
                }
            }
        }
    </script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="bg-navy-blue shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-home mr-2 text-pastel-orange"></i>
                        Sahabat Rumah
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-lg mx-8">
                    <form action="{{ route('products.index') }}" method="GET" class="relative">
                        <input type="text" name="search" placeholder="Cari produk rumah..."
                               value="{{ request('search') }}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pastel-orange focus:border-transparent">
                        <button type="submit" class="absolute right-3 top-2.5 text-gray-400 hover:text-pastel-orange">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center space-x-4">
                    <a href="{{ route('home') }}" class="text-white hover:text-pastel-orange transition duration-300">Home</a>
                    <a href="{{ route('products.index') }}" class="text-white hover:text-pastel-orange transition duration-300">Produk</a>

                    <!-- Cart -->
                    <a href="{{ route('cart.index') }}" class="relative text-white hover:text-pastel-orange transition duration-300">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <span x-show="cartCount > 0" x-text="cartCount"
                              class="absolute -top-2 -right-2 bg-pastel-orange text-navy-blue text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold"></span>
                    </a>

                    @auth
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-white hover:text-pastel-orange transition duration-300">
                                <i class="fas fa-user mr-1"></i>
                                {{ auth()->user()->name }}
                                <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </button>
                            <div x-show="open" @click.away="open = false"
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                                <a href="{{ route('account.dashboard') }}" class="block px-4 py-2 text-sm text-navy-blue hover:bg-light-orange">Dashboard</a>
                                <a href="{{ route('account.orders') }}" class="block px-4 py-2 text-sm text-navy-blue hover:bg-light-orange">Pesanan Saya</a>
                                <a href="{{ route('account.profile') }}" class="block px-4 py-2 text-sm text-navy-blue hover:bg-light-orange">Profil</a>
                                <form action="{{ route('logout') }}" method="POST" class="block">
                                    @csrf
                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-navy-blue hover:bg-light-orange">Keluar</button>
                                </form>
                            </div>
                        </div>
                    @else
                        <a href="{{ route('login') }}" class="text-white hover:text-pastel-orange transition duration-300">Masuk</a>
                        <a href="{{ route('register') }}" class="bg-pastel-orange text-navy-blue px-4 py-2 rounded-lg hover:bg-orange-300 transition duration-300 font-medium">Daftar</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    @if(session('success'))
        <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-navy-blue text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-home mr-2 text-pastel-orange"></i>
                        Sahabat Rumah
                    </h3>
                    <p class="text-gray-300">Toko online terpercaya untuk kebutuhan rumah tangga berkualitas dengan harga terjangkau.</p>
                    <div class="mt-4">
                        <p class="text-sm text-gray-400">
                            <i class="fas fa-map-marker-alt mr-2 text-pastel-orange"></i>
                            Jakarta, Indonesia
                        </p>
                        <p class="text-sm text-gray-400 mt-1">
                            <i class="fas fa-phone mr-2 text-pastel-orange"></i>
                            +62 21 1234 5678
                        </p>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Menu Utama</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ route('home') }}" class="text-gray-300 hover:text-pastel-orange transition duration-300">Beranda</a></li>
                        <li><a href="{{ route('products.index') }}" class="text-gray-300 hover:text-pastel-orange transition duration-300">Produk</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300">Tentang Kami</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300">Kontak</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Layanan Pelanggan</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300">Pusat Bantuan</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300">Info Pengiriman</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300">Pengembalian</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300">Lacak Pesanan</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Ikuti Kami</h4>
                    <div class="flex space-x-4 mb-4">
                        <a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300"><i class="fab fa-facebook text-xl"></i></a>
                        <a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300"><i class="fab fa-twitter text-xl"></i></a>
                        <a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300"><i class="fab fa-instagram text-xl"></i></a>
                        <a href="#" class="text-gray-300 hover:text-pastel-orange transition duration-300"><i class="fab fa-whatsapp text-xl"></i></a>
                    </div>
                    <div class="bg-pastel-orange bg-opacity-10 p-3 rounded-lg">
                        <p class="text-sm text-pastel-orange font-medium">Newsletter</p>
                        <p class="text-xs text-gray-300 mt-1">Dapatkan penawaran terbaru</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">&copy; 2025 Sahabat Rumah. Semua hak dilindungi.</p>
            </div>
        </div>
    </footer>

    <!-- Cart API Helper -->
    <script>
        window.cartAPI = {
            async add(productId, quantity = 1) {
                try {
                    const response = await fetch('{{ route("cart.add") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: quantity
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok) {
                        // Update cart count
                        Alpine.store('cart').count = data.cart_count;
                        return { success: true, message: data.message };
                    } else {
                        return { success: false, message: data.message };
                    }
                } catch (error) {
                    return { success: false, message: 'An error occurred' };
                }
            }
        };

        // Alpine.js store for cart
        document.addEventListener('alpine:init', () => {
            Alpine.store('cart', {
                count: {{ session()->has('cart') ? array_sum(array_column(session('cart'), 'quantity')) : 0 }}
            });
        });

        // Update cart count function for global use
        window.updateCartCount = function() {
            const cartCountElement = document.querySelector('[x-text="cartCount"]');
            if (cartCountElement && cartCountElement.__x) {
                // Get current cart count from server
                fetch('/cart/count')
                    .then(response => response.json())
                    .then(data => {
                        cartCountElement.__x.$data.cartCount = data.count;
                    })
                    .catch(error => console.error('Error updating cart count:', error));
            }
        };
    </script>
</body>
</html>
