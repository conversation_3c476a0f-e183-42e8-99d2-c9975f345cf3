<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        // Create categories
        $electronics = Category::create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'description' => 'Electronic devices and gadgets',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $smartphones = Category::create([
            'name' => 'Smartphones',
            'slug' => 'smartphones',
            'description' => 'Mobile phones and accessories',
            'parent_id' => $electronics->id,
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $laptops = Category::create([
            'name' => 'Laptops',
            'slug' => 'laptops',
            'description' => 'Portable computers',
            'parent_id' => $electronics->id,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        $clothing = Category::create([
            'name' => 'Clothing',
            'slug' => 'clothing',
            'description' => 'Fashion and apparel',
            'is_active' => true,
            'sort_order' => 2,
        ]);

        // Create products
        $products = [
            [
                'name' => 'iPhone 15 Pro',
                'slug' => 'iphone-15-pro',
                'description' => 'Latest iPhone with advanced features',
                'short_description' => 'Premium smartphone with Pro camera system',
                'sku' => 'IPH15PRO001',
                'price' => 999.99,
                'sale_price' => 899.99,
                'stock_quantity' => 50,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $smartphones->id,
                'is_active' => true,
                'is_featured' => true,
                'images' => ['iphone-15-pro-1.jpg', 'iphone-15-pro-2.jpg'],
                'weight' => 0.221,
                'dimensions' => '159.9 x 76.7 x 8.25 mm',
            ],
            [
                'name' => 'MacBook Pro 16"',
                'slug' => 'macbook-pro-16',
                'description' => 'Powerful laptop for professionals',
                'short_description' => 'High-performance laptop with M3 chip',
                'sku' => 'MBP16M3001',
                'price' => 2499.99,
                'stock_quantity' => 25,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $laptops->id,
                'is_active' => true,
                'is_featured' => true,
                'images' => ['macbook-pro-16-1.jpg'],
                'weight' => 2.15,
                'dimensions' => '355.7 x 248.1 x 16.8 mm',
            ],
            [
                'name' => 'Samsung Galaxy S24',
                'slug' => 'samsung-galaxy-s24',
                'description' => 'Android flagship smartphone',
                'short_description' => 'Premium Android phone with AI features',
                'sku' => 'SGS24001',
                'price' => 799.99,
                'stock_quantity' => 75,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $smartphones->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['galaxy-s24-1.jpg'],
                'weight' => 0.167,
                'dimensions' => '147 x 70.6 x 7.6 mm',
            ],
            [
                'name' => 'Classic T-Shirt',
                'slug' => 'classic-t-shirt',
                'description' => 'Comfortable cotton t-shirt',
                'short_description' => '100% cotton comfortable t-shirt',
                'sku' => 'TSHIRT001',
                'price' => 29.99,
                'stock_quantity' => 100,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $clothing->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['t-shirt-1.jpg'],
                'weight' => 0.2,
                'attributes' => ['color' => 'Blue', 'size' => 'M'],
            ],
            [
                'name' => 'Dell XPS 13',
                'slug' => 'dell-xps-13',
                'description' => 'Ultrabook with premium design',
                'short_description' => 'Compact and powerful ultrabook',
                'sku' => 'DELLXPS13001',
                'price' => 1299.99,
                'stock_quantity' => 5, // Low stock for testing
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $laptops->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['dell-xps-13-1.jpg'],
                'weight' => 1.27,
                'dimensions' => '295.7 x 199.04 x 14.8 mm',
            ],
        ];

        foreach ($products as $productData) {
            Product::create($productData);
        }

        // Create sample orders
        $createdProducts = Product::all();

        for ($i = 1; $i <= 10; $i++) {
            $order = Order::create([
                'order_number' => 'ORD-' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'customer_name' => 'Customer ' . $i,
                'customer_email' => 'customer' . $i . '@example.com',
                'customer_phone' => '+1234567890',
                'billing_address_line_1' => '123 Main St',
                'billing_city' => 'New York',
                'billing_state' => 'NY',
                'billing_postal_code' => '10001',
                'billing_country' => 'USA',
                'shipping_address_line_1' => '123 Main St',
                'shipping_city' => 'New York',
                'shipping_state' => 'NY',
                'shipping_postal_code' => '10001',
                'shipping_country' => 'USA',
                'status' => ['pending', 'processing', 'shipped', 'delivered'][array_rand(['pending', 'processing', 'shipped', 'delivered'])],
                'subtotal' => 0,
                'tax_amount' => 0,
                'shipping_amount' => 10.00,
                'discount_amount' => 0,
                'total_amount' => 0,
                'created_at' => now()->subDays(rand(0, 30)),
            ]);

            // Add 1-3 items to each order
            $itemCount = rand(1, 3);
            $subtotal = 0;

            for ($j = 0; $j < $itemCount; $j++) {
                $product = $createdProducts->random();
                $quantity = rand(1, 2);
                $unitPrice = $product->current_price;
                $totalPrice = $unitPrice * $quantity;
                $subtotal += $totalPrice;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'unit_price' => $unitPrice,
                    'quantity' => $quantity,
                    'total_price' => $totalPrice,
                    'product_attributes' => $product->attributes,
                ]);
            }

            // Update order totals
            $taxAmount = $subtotal * 0.08; // 8% tax
            $totalAmount = $subtotal + $taxAmount + $order->shipping_amount;

            $order->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
            ]);
        }
    }
}
