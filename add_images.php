<?php

// Simple script to add images to products
$database = 'database/database.sqlite';

if (!file_exists($database)) {
    echo "Database file not found!\n";
    exit(1);
}

try {
    $pdo = new PDO('sqlite:' . $database);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get all products
    $stmt = $pdo->query("SELECT id, name, images FROM products");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "Found " . count($products) . " products\n\n";

    // Product images mapping
    $imageMap = [
        'iPhone' => 'https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=500&h=500&fit=crop',
        'Samsung Galaxy' => 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500&h=500&fit=crop',
        'Google Pixel' => 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=500&h=500&fit=crop',
        'MacBook' => 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500&h=500&fit=crop',
        'Dell XPS' => 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=500&fit=crop',
        'Gaming Mouse' => 'https://images.unsplash.com/photo-1527814050087-3793815479db?w=500&h=500&fit=crop',
        'Keyboard' => 'https://images.unsplash.com/photo-*************-b024d705b90a?w=500&h=500&fit=crop',
        'Earbuds' => 'https://images.unsplash.com/photo-*************-6bf12165a8df?w=500&h=500&fit=crop',
        'USB-C Hub' => 'https://images.unsplash.com/photo-*************-8f3296236761?w=500&h=500&fit=crop',
        'Speaker' => 'https://images.unsplash.com/photo-*************-423dbba4e7e1?w=500&h=500&fit=crop',
        'Charger' => 'https://images.unsplash.com/photo-*************-b95a79798f07?w=500&h=500&fit=crop',
        'Power Bank' => 'https://images.unsplash.com/photo-*************-4d1b5e5e6e8e?w=500&h=500&fit=crop',
        'Webcam' => 'https://images.unsplash.com/photo-*************-dfaf72ae4b04?w=500&h=500&fit=crop',
        'Sony WH' => 'https://images.unsplash.com/photo-*************-acd977736f90?w=500&h=500&fit=crop',
        'Headset' => 'https://images.unsplash.com/photo-*************-************?w=500&h=500&fit=crop',
        'iPad' => 'https://images.unsplash.com/photo-**********-0df4b3ffc6b0?w=500&h=500&fit=crop',
        'Galaxy Tab' => 'https://images.unsplash.com/photo-**********-82e9adf32764?w=500&h=500&fit=crop',
        'Apple Watch' => 'https://images.unsplash.com/photo-*************-2f02dc6ca35d?w=500&h=500&fit=crop',
        'Galaxy Watch' => 'https://images.unsplash.com/photo-*************-37898b6baf30?w=500&h=500&fit=crop',
        'Canon' => 'https://images.unsplash.com/photo-*************-1aa500764cbd?w=500&h=500&fit=crop',
        'Sony Alpha' => 'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=500&h=500&fit=crop',
        'Chair' => 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500&h=500&fit=crop',
        'Monitor' => 'https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=500&h=500&fit=crop',
        'Lamp' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop',
        'Stand' => 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500&h=500&fit=crop',
    ];

    $defaultImages = [
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1526738549149-8e07eca6c147?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-*************-37898b6baf30?w=500&h=500&fit=crop',
    ];

    $updatedCount = 0;

    foreach ($products as $product) {
        $productName = $product['name'];
        $currentImages = $product['images'];
        
        // Skip if already has images
        if (!empty($currentImages) && $currentImages !== '[]' && $currentImages !== 'null') {
            echo "⏭️  Skipping {$productName} (already has images)\n";
            continue;
        }

        // Find matching image
        $imageUrl = null;
        foreach ($imageMap as $keyword => $url) {
            if (stripos($productName, $keyword) !== false) {
                $imageUrl = $url;
                break;
            }
        }

        // Use default image if no match found
        if (!$imageUrl) {
            $imageUrl = $defaultImages[$updatedCount % count($defaultImages)];
        }

        // Update product with image
        $stmt = $pdo->prepare("UPDATE products SET images = ? WHERE id = ?");
        $stmt->execute([json_encode([$imageUrl]), $product['id']]);
        
        echo "✅ Updated {$productName} with image\n";
        $updatedCount++;
    }

    echo "\n🎉 Successfully updated {$updatedCount} products with images!\n";
    echo "📸 All products now have attractive product images from Unsplash.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
