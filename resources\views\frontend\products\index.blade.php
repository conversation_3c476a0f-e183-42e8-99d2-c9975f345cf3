@extends('frontend.layout')

@section('title', 'Produk - Sahabat Rumah')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-1/4">
            <div class="bg-white rounded-lg shadow-md p-6 border-2 border-light-orange">
                <h3 class="text-lg font-semibold mb-4 text-navy-blue">Filter Produk</h3>

                <form method="GET" action="{{ route('products.index') }}">
                    <!-- Keep existing search term -->
                    @if(request('search'))
                        <input type="hidden" name="search" value="{{ request('search') }}">
                    @endif

                    <!-- Categories -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3 text-navy-blue">Kate<PERSON>i</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="category" value=""
                                       {{ !request('category') ? 'checked' : '' }}
                                       class="mr-2 text-pastel-orange focus:ring-pastel-orange">
                                Semua Kategori
                            </label>
                            @foreach($categories as $category)
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="{{ $category->id }}"
                                           {{ request('category') == $category->id ? 'checked' : '' }}
                                           class="mr-2 text-pastel-orange focus:ring-pastel-orange">
                                    {{ $category->name }}
                                </label>
                            @endforeach
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3 text-navy-blue">Rentang Harga</h4>
                        <div class="space-y-2">
                            <div class="flex space-x-2">
                                <input type="number" name="min_price" placeholder="Minimum"
                                       value="{{ request('min_price') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pastel-orange">
                                <input type="number" name="max_price" placeholder="Maksimum"
                                       value="{{ request('max_price') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pastel-orange">
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-pastel-orange text-navy-blue py-2 rounded-md hover:bg-orange-300 transition duration-300 font-medium">
                        Terapkan Filter
                    </button>
                </form>

                <!-- Clear Filters -->
                @if(request()->hasAny(['category', 'min_price', 'max_price']))
                    <a href="{{ route('products.index', ['search' => request('search')]) }}"
                       class="block w-full text-center mt-2 text-gray-600 hover:text-pastel-orange">
                        Hapus Filter
                    </a>
                @endif
            </div>
        </div>

        <!-- Products Grid -->
        <div class="lg:w-3/4">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-navy-blue">Produk</h1>
                    <p class="text-gray-600">{{ $products->total() }} produk ditemukan</p>
                </div>

                <!-- Sort Options -->
                <div class="mt-4 sm:mt-0">
                    <form method="GET" action="{{ route('products.index') }}" class="flex items-center">
                        <!-- Keep existing filters -->
                        @foreach(request()->except(['sort', 'page']) as $key => $value)
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endforeach

                        <label class="mr-2 text-sm text-gray-600">Urutkan:</label>
                        <select name="sort" onchange="this.form.submit()"
                                class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pastel-orange">
                            <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Nama</option>
                            <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Harga: Terendah ke Tertinggi</option>
                            <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Harga: Tertinggi ke Terendah</option>
                            <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Terbaru</option>
                        </select>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            @if($products->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($products as $product)
                        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300 overflow-hidden">
                            <a href="{{ route('products.show', $product->slug) }}">
                                @if($product->main_image)
                                    <img src="{{ $product->main_image }}" alt="{{ $product->name }}" class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400 text-4xl"></i>
                                    </div>
                                @endif
                            </a>
                            <div class="p-4">
                                <a href="{{ route('products.show', $product->slug) }}" class="block">
                                    <h3 class="font-semibold text-gray-800 hover:text-blue-600 mb-2">{{ $product->name }}</h3>
                                </a>
                                <p class="text-gray-600 text-sm mb-3">{{ Str::limit($product->short_description, 80) }}</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        @if($product->sale_price)
                                            <span class="text-lg font-bold text-red-600">{{ $product->formatted_current_price }}</span>
                                            <span class="text-sm text-gray-500 line-through">{{ $product->formatted_price }}</span>
                                        @else
                                            <span class="text-lg font-bold text-gray-800">{{ $product->formatted_price }}</span>
                                        @endif
                                    </div>
                                    <button onclick="addToCart({{ $product->id }})"
                                            class="bg-pastel-orange text-navy-blue px-3 py-1 rounded hover:bg-orange-300 transition duration-300 font-medium">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                </div>
                                @if($product->category)
                                    <div class="mt-2">
                                        <span class="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                                            {{ $product->category->name }}
                                        </span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    {{ $products->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-search text-pastel-orange text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-navy-blue mb-2">Produk tidak ditemukan</h3>
                    <p class="text-gray-500">Coba sesuaikan pencarian atau kriteria filter Anda</p>
                    <a href="{{ route('products.index') }}" class="inline-block mt-4 bg-pastel-orange text-navy-blue px-6 py-2 rounded-lg hover:bg-orange-300 transition duration-300 font-medium">
                        Lihat Semua Produk
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
async function addToCart(productId) {
    const result = await window.cartAPI.add(productId, 1);
    
    if (result.success) {
        // Update cart count in navigation
        const cartCountElement = document.querySelector('[x-text="cartCount"]');
        if (cartCountElement) {
            cartCountElement.__x.$data.cartCount++;
        }
        
        // Show success message
        showNotification(result.message, 'success');
    } else {
        showNotification(result.message, 'error');
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
