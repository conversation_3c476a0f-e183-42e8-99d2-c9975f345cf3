<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;

class UpdatePricesToIDRSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing products to use IDR pricing
        $products = Product::all();
        
        foreach ($products as $product) {
            // Convert USD to IDR (approximate rate: 1 USD = 15,000 IDR)
            $newPrice = $product->price * 15000;
            $newSalePrice = $product->sale_price ? $product->sale_price * 15000 : null;
            
            // Round to nearest thousand for cleaner pricing
            $newPrice = round($newPrice / 1000) * 1000;
            $newSalePrice = $newSalePrice ? round($newSalePrice / 1000) * 1000 : null;
            
            $product->update([
                'price' => $newPrice,
                'sale_price' => $newSalePrice,
            ]);
        }
        
        // Add some new products with IDR pricing
        // Get first available category or create a default one
        $defaultCategory = \App\Models\Category::first();
        if (!$defaultCategory) {
            $defaultCategory = \App\Models\Category::create([
                'name' => 'General',
                'slug' => 'general',
                'description' => 'General products category',
                'is_active' => true,
            ]);
        }
        
        $newProducts = [
            [
                'name' => 'Mechanical Keyboard RGB',
                'slug' => 'mechanical-keyboard-rgb',
                'description' => 'Professional mechanical keyboard with RGB backlighting, blue switches, and programmable keys. Perfect for gaming and typing.',
                'short_description' => 'RGB mechanical keyboard with blue switches',
                'sku' => 'KEYBOARD001',
                'price' => 850000, // Rp 850,000
                'sale_price' => 750000, // Rp 750,000
                'stock_quantity' => 75,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $defaultCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'images' => ['https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500'],
                'attributes' => ['Switch Type' => 'Blue', 'Backlight' => 'RGB', 'Layout' => 'Full Size'],
                'weight' => 1.2,
                'dimensions' => '44 x 13 x 3 cm',
            ],
            [
                'name' => 'Smartwatch Fitness Tracker',
                'slug' => 'smartwatch-fitness-tracker',
                'description' => 'Advanced smartwatch with heart rate monitoring, GPS tracking, and 7-day battery life. Water resistant up to 50m.',
                'short_description' => 'GPS smartwatch with fitness tracking',
                'sku' => 'WATCH001',
                'price' => 1250000, // Rp 1,250,000
                'stock_quantity' => 60,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $defaultCategory->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500'],
                'attributes' => ['Display' => '1.4 inch AMOLED', 'Battery' => '7 days', 'Water Resistance' => '50m'],
                'weight' => 0.045,
                'dimensions' => '4.5 x 4.5 x 1.2 cm',
            ],
            [
                'name' => 'Cotton T-Shirt Premium',
                'slug' => 'cotton-t-shirt-premium',
                'description' => 'Premium 100% organic cotton t-shirt with comfortable fit and durable construction. Available in multiple colors.',
                'short_description' => '100% organic cotton premium t-shirt',
                'sku' => 'TSHIRT001',
                'price' => 185000, // Rp 185,000
                'sale_price' => 150000, // Rp 150,000
                'stock_quantity' => 250,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $defaultCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'images' => ['https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500'],
                'attributes' => ['Material' => '100% Organic Cotton', 'Fit' => 'Regular', 'Care' => 'Machine Wash'],
                'weight' => 0.2,
                'dimensions' => 'Various sizes available',
            ],
            [
                'name' => 'Wireless Earbuds Pro',
                'slug' => 'wireless-earbuds-pro',
                'description' => 'Premium wireless earbuds with active noise cancellation, wireless charging case, and premium sound quality.',
                'short_description' => 'ANC wireless earbuds with charging case',
                'sku' => 'EARBUDS001',
                'price' => 950000, // Rp 950,000
                'sale_price' => 850000, // Rp 850,000
                'stock_quantity' => 100,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $defaultCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'images' => ['https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=500'],
                'attributes' => ['ANC' => 'Active', 'Battery' => '6h + 24h case', 'Charging' => 'Wireless'],
                'weight' => 0.05,
                'dimensions' => '2.5 x 2.5 x 1.5 cm each',
            ],
            [
                'name' => 'USB-C Hub Multiport',
                'slug' => 'usb-c-hub-multiport',
                'description' => 'Versatile USB-C hub with HDMI, USB 3.0 ports, SD card reader, and power delivery. Perfect for laptops and tablets.',
                'short_description' => 'Multi-port USB-C hub with HDMI',
                'sku' => 'HUB001',
                'price' => 425000, // Rp 425,000
                'stock_quantity' => 150,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $defaultCategory->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['https://images.unsplash.com/photo-1625842268584-8f3296236761?w=500'],
                'attributes' => ['Ports' => '7-in-1', 'HDMI' => '4K@60Hz', 'Power Delivery' => '100W'],
                'weight' => 0.15,
                'dimensions' => '11 x 4.5 x 1.5 cm',
            ],
        ];
        
        foreach ($newProducts as $productData) {
            Product::create($productData);
        }
        
        $this->command->info('Successfully updated existing products to IDR pricing and added 5 new products with IDR prices.');
    }
}
