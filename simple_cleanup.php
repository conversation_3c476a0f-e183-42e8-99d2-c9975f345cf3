<?php

echo "🧹 Membersihkan produk yang tidak masuk kategori Electronics dan Clothing...\n\n";

try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Cari ID kategori Electronics dan Clothing
    $stmt = $pdo->prepare("SELECT id, name FROM categories WHERE name LIKE '%Electronics%' OR name LIKE '%Clothing%'");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $electronicsId = null;
    $clothingId = null;
    
    foreach ($categories as $category) {
        if (stripos($category['name'], 'Electronics') !== false) {
            $electronicsId = $category['id'];
            echo "📂 Kategori Electronics ID: {$electronicsId} - {$category['name']}\n";
        }
        if (stripos($category['name'], 'Clothing') !== false) {
            $clothingId = $category['id'];
            echo "📂 Kategori Clothing ID: {$clothingId} - {$category['name']}\n";
        }
    }
    
    if (!$electronicsId || !$clothingId) {
        echo "❌ Kategori Electronics atau Clothing tidak ditemukan!\n";
        exit;
    }
    
    echo "\n";
    
    // Hitung total produk saat ini
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products");
    $stmt->execute();
    $totalProducts = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📊 Total produk saat ini: {$totalProducts}\n\n";
    
    // Cari produk yang tidak dalam kategori Electronics atau Clothing
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.category_id NOT IN (?, ?)
    ");
    $stmt->execute([$electronicsId, $clothingId]);
    $productsToDelete = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($productsToDelete)) {
        echo "✅ Semua produk sudah berada dalam kategori Electronics atau Clothing!\n";
        
        // Show current stats
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE category_id = ?");
        $stmt->execute([$electronicsId]);
        $electronicsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        $stmt->execute([$clothingId]);
        $clothingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "📊 Total produk Electronics: {$electronicsCount}\n";
        echo "📊 Total produk Clothing: {$clothingCount}\n";
        exit;
    }
    
    echo "🗑️  Produk yang akan dihapus (" . count($productsToDelete) . " produk):\n\n";
    
    foreach ($productsToDelete as $product) {
        $categoryName = $product['category_name'] ?: 'Tidak ada kategori';
        $price = 'Rp ' . number_format($product['price'], 0, ',', '.');
        
        echo "❌ {$product['name']}\n";
        echo "   SKU: {$product['sku']} | Kategori: {$categoryName} | Harga: {$price}\n";
        
        // Cek apakah produk memiliki order items
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM order_items WHERE product_id = ?");
        $stmt->execute([$product['id']]);
        $orderItemsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($orderItemsCount > 0) {
            echo "   ⚠️  PERINGATAN: Produk ini memiliki {$orderItemsCount} order item(s)!\n";
        }
        echo "\n";
    }
    
    // Show what will remain
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE category_id = ?");
    $stmt->execute([$electronicsId]);
    $electronicsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt->execute([$clothingId]);
    $clothingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "⚠️  PERINGATAN: Operasi ini akan menghapus " . count($productsToDelete) . " produk!\n";
    echo "📋 Produk yang akan TETAP ADA:\n";
    echo "   - Electronics: {$electronicsCount} produk\n";
    echo "   - Clothing: {$clothingCount} produk\n\n";
    
    // Proses penghapusan
    $deletedCount = 0;
    $skippedCount = 0;
    
    foreach ($productsToDelete as $product) {
        try {
            // Cek order items lagi sebelum menghapus
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM order_items WHERE product_id = ?");
            $stmt->execute([$product['id']]);
            $orderItemsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($orderItemsCount > 0) {
                echo "⏭️  Melewati {$product['name']} (memiliki {$orderItemsCount} order item)\n";
                $skippedCount++;
                continue;
            }
            
            // Hapus produk
            $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
            $stmt->execute([$product['id']]);
            
            echo "🗑️  Berhasil menghapus: {$product['name']}\n";
            $deletedCount++;
            
        } catch (Exception $e) {
            echo "❌ Error menghapus {$product['name']}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🎉 Proses pembersihan selesai!\n";
    echo "✅ Berhasil menghapus: {$deletedCount} produk\n";
    echo "⏭️  Dilewati (ada order): {$skippedCount} produk\n";
    
    // Show final stats
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products");
    $stmt->execute();
    $finalTotal = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE category_id = ?");
    $stmt->execute([$electronicsId]);
    $finalElectronics = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt->execute([$clothingId]);
    $finalClothing = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "\n📊 STATISTIK AKHIR:\n";
    echo "📱 Total produk Electronics: {$finalElectronics}\n";
    echo "👕 Total produk Clothing: {$finalClothing}\n";
    echo "📦 Total produk keseluruhan: {$finalTotal}\n";
    
    if ($finalTotal !== ($finalElectronics + $finalClothing)) {
        $remaining = $finalTotal - ($finalElectronics + $finalClothing);
        echo "⚠️  Masih ada {$remaining} produk di kategori lain\n";
    } else {
        echo "✅ Semua produk sekarang hanya dalam kategori Electronics dan Clothing!\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🏁 Selesai!\n";
