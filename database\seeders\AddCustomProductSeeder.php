<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Seeder;

class AddCustomProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // <PERSON>bil kategori yang sudah ada
        $electronics = Category::where('slug', 'electronics')->first();
        $smartphones = Category::where('slug', 'smartphones')->first();
        $laptops = Category::where('slug', 'laptops')->first();

        // Tambahkan produk baru
        $newProducts = [
            [
                'name' => 'Gaming Mouse RGB',
                'slug' => 'gaming-mouse-rgb',
                'description' => 'High-precision gaming mouse with RGB lighting and programmable buttons. Perfect for gaming enthusiasts.',
                'short_description' => 'RGB gaming mouse with high DPI sensor',
                'sku' => 'MOUSE001',
                'price' => 79.99,
                'sale_price' => 59.99,
                'stock_quantity' => 150,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $electronics->id,
                'is_active' => true,
                'is_featured' => true,
                'images' => ['gaming-mouse-1.jpg', 'gaming-mouse-2.jpg'],
                'weight' => 0.12,
                'dimensions' => '125 x 68 x 42 mm',
                'attributes' => ['color' => 'Black', 'dpi' => '16000'],
            ],
            [
                'name' => 'Wireless Headphones',
                'slug' => 'wireless-headphones',
                'description' => 'Premium wireless headphones with noise cancellation and long battery life.',
                'short_description' => 'Noise-cancelling wireless headphones',
                'sku' => 'HEADPHONE001',
                'price' => 199.99,
                'stock_quantity' => 80,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $electronics->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['headphones-1.jpg'],
                'weight' => 0.25,
                'dimensions' => '180 x 160 x 80 mm',
                'attributes' => ['color' => 'Black', 'battery_life' => '30 hours'],
            ],
            [
                'name' => 'Casual Jeans',
                'slug' => 'casual-jeans',
                'description' => 'Comfortable denim jeans perfect for everyday wear. Made from high-quality cotton blend.',
                'short_description' => 'Comfortable cotton blend jeans',
                'sku' => 'JEANS001',
                'price' => 89.99,
                'sale_price' => 69.99,
                'stock_quantity' => 200,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $clothing->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['jeans-1.jpg', 'jeans-2.jpg'],
                'weight' => 0.6,
                'attributes' => ['color' => 'Blue', 'size' => 'L', 'material' => 'Cotton Blend'],
            ],
            [
                'name' => 'Smartphone Case',
                'slug' => 'smartphone-case',
                'description' => 'Protective case for smartphones with shock absorption and wireless charging compatibility.',
                'short_description' => 'Protective smartphone case',
                'sku' => 'CASE001',
                'price' => 24.99,
                'stock_quantity' => 300,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $smartphones->id,
                'is_active' => true,
                'is_featured' => false,
                'images' => ['phone-case-1.jpg'],
                'weight' => 0.05,
                'dimensions' => '160 x 80 x 10 mm',
                'attributes' => ['color' => 'Clear', 'material' => 'TPU'],
            ],
            [
                'name' => 'Laptop Stand Adjustable',
                'slug' => 'laptop-stand-adjustable',
                'description' => 'Ergonomic adjustable laptop stand for better posture and cooling. Compatible with all laptop sizes.',
                'short_description' => 'Adjustable ergonomic laptop stand',
                'sku' => 'STAND001',
                'price' => 49.99,
                'stock_quantity' => 120,
                'manage_stock' => true,
                'in_stock' => true,
                'category_id' => $laptops->id,
                'is_active' => true,
                'is_featured' => true,
                'images' => ['laptop-stand-1.jpg'],
                'weight' => 1.2,
                'dimensions' => '280 x 220 x 50 mm',
                'attributes' => ['material' => 'Aluminum', 'adjustable' => 'Yes'],
            ],
        ];

        foreach ($newProducts as $productData) {
            Product::create($productData);
        }

        echo "✅ Successfully added " . count($newProducts) . " new products!\n";
    }
}
