@echo off
echo Starting Laravel E-Commerce Server...
echo.

REM Set PHP path from Herd
set PHP_PATH=C:\Users\<USER>\.config\herd\bin\php84\php.exe

REM Check if PHP exists
if not exist "%PHP_PATH%" (
    echo Error: PHP not found at %PHP_PATH%
    echo Please check your Herd installation.
    pause
    exit /b 1
)

echo PHP found: %PHP_PATH%
echo.

REM Clear Laravel cache
echo Clearing Laravel cache...
"%PHP_PATH%" artisan cache:clear
"%PHP_PATH%" artisan config:clear
"%PHP_PATH%" artisan view:clear
echo Cache cleared successfully!
echo.

REM Start the server
echo Starting server on http://localhost:8000
echo Press Ctrl+C to stop the server
echo.
"%PHP_PATH%" -S localhost:8000 -t public

pause
