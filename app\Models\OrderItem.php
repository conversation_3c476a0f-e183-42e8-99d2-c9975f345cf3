<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'unit_price',
        'quantity',
        'total_price',
        'product_attributes',
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'quantity' => 'integer',
        'product_attributes' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($orderItem) {
            // Calculate total price
            $orderItem->total_price = $orderItem->unit_price * $orderItem->quantity;
        });

        static::updating(function ($orderItem) {
            // Recalculate total price if unit_price or quantity changes
            if ($orderItem->isDirty(['unit_price', 'quantity'])) {
                $orderItem->total_price = $orderItem->unit_price * $orderItem->quantity;
            }
        });
    }

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Accessors
    public function getFormattedUnitPriceAttribute(): string
    {
        return '$' . number_format($this->unit_price, 2);
    }

    public function getFormattedTotalPriceAttribute(): string
    {
        return '$' . number_format($this->total_price, 2);
    }

    // Methods
    public function updateQuantity(int $newQuantity): void
    {
        $this->quantity = $newQuantity;
        $this->total_price = $this->unit_price * $this->quantity;
        $this->save();
    }
}
