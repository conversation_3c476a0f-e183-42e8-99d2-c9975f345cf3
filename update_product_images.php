<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// Setup database connection
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'sqlite',
    'database' => 'database/database.sqlite',
    'prefix' => '',
]);
$capsule->setAsGlobal();
$capsule->bootEloquent();

// Product images mapping
$productImages = [
    // Smartphones
    'iPhone 15 Pro' => [
        'https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=500&h=500&fit=crop&crop=entropy'
    ],
    'Samsung Galaxy S24' => [
        'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500&h=500&fit=crop'
    ],
    'Google Pixel 8' => [
        'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=500&h=500&fit=crop'
    ],

    // Laptops
    'MacBook Pro 16"' => [
        'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500&h=500&fit=crop'
    ],
    'Dell XPS 13' => [
        'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=500&fit=crop'
    ],
    'Laptop Stand Adjustable' => [
        'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500&h=500&fit=crop'
    ],

    // Electronics & Accessories
    'Gaming Mouse RGB' => [
        'https://images.unsplash.com/photo-1527814050087-3793815479db?w=500&h=500&fit=crop'
    ],
    'Mechanical Keyboard RGB' => [
        'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500&h=500&fit=crop'
    ],
    'Wireless Earbuds Pro' => [
        'https://images.unsplash.com/photo-*************-6bf12165a8df?w=500&h=500&fit=crop'
    ],
    'USB-C Hub Multiport' => [
        'https://images.unsplash.com/photo-*************-8f3296236761?w=500&h=500&fit=crop'
    ],
    'Bluetooth Speaker' => [
        'https://images.unsplash.com/photo-*************-423dbba4e7e1?w=500&h=500&fit=crop'
    ],
    'Wireless Charger' => [
        'https://images.unsplash.com/photo-*************-b95a79798f07?w=500&h=500&fit=crop'
    ],
    'Power Bank 20000mAh' => [
        'https://images.unsplash.com/photo-*************-4d1b5e5e6e8e?w=500&h=500&fit=crop'
    ],
    'Webcam HD 1080p' => [
        'https://images.unsplash.com/photo-*************-dfaf72ae4b04?w=500&h=500&fit=crop'
    ],

    // Headphones
    'Sony WH-1000XM5' => [
        'https://images.unsplash.com/photo-*************-acd977736f90?w=500&h=500&fit=crop'
    ],
    'Gaming Headset' => [
        'https://images.unsplash.com/photo-*************-************?w=500&h=500&fit=crop'
    ],

    // Tablets
    'iPad Pro 12.9"' => [
        'https://images.unsplash.com/photo-**********-0df4b3ffc6b0?w=500&h=500&fit=crop'
    ],
    'Samsung Galaxy Tab S9' => [
        'https://images.unsplash.com/photo-**********-82e9adf32764?w=500&h=500&fit=crop'
    ],

    // Smartwatches
    'Apple Watch Series 9' => [
        'https://images.unsplash.com/photo-*************-2f02dc6ca35d?w=500&h=500&fit=crop'
    ],
    'Samsung Galaxy Watch 6' => [
        'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop'
    ],

    // Cameras
    'Canon EOS R5' => [
        'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=500&h=500&fit=crop'
    ],
    'Sony Alpha A7 IV' => [
        'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=500&h=500&fit=crop'
    ],

    // Gaming
    'Gaming Chair RGB' => [
        'https://images.unsplash.com/photo-1592300103777-e6e5b8b8e8e8?w=500&h=500&fit=crop'
    ],

    // Home & Office
    'Monitor 27" 4K' => [
        'https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=500&h=500&fit=crop'
    ],
    'Desk Lamp LED' => [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop'
    ],
    'Office Chair Ergonomic' => [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500&h=500&fit=crop'
    ],
];

$updatedCount = 0;

try {
    // Update products with specific images
    foreach ($productImages as $productName => $images) {
        $updated = Capsule::table('products')
            ->where('name', 'like', '%' . $productName . '%')
            ->update(['images' => json_encode($images)]);
        
        if ($updated > 0) {
            echo "✅ Updated images for: {$productName}\n";
            $updatedCount += $updated;
        }
    }

    // Add default images for products without images
    $defaultImages = [
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1526738549149-8e07eca6c147?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=500&fit=crop',
        'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop',
    ];

    $productsWithoutImages = Capsule::table('products')
        ->whereNull('images')
        ->orWhere('images', '[]')
        ->orWhere('images', 'null')
        ->orWhere('images', '')
        ->get();

    foreach ($productsWithoutImages as $index => $product) {
        $defaultImage = $defaultImages[$index % count($defaultImages)];
        Capsule::table('products')
            ->where('id', $product->id)
            ->update(['images' => json_encode([$defaultImage])]);
        
        echo "✅ Added default image for: {$product->name}\n";
        $updatedCount++;
    }

    echo "\n🎉 Successfully updated images for {$updatedCount} products!\n";
    echo "📸 All products now have attractive product images from Unsplash.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
