<?php

echo "🗑️  Menghapus produk Power Bank 20000mAh...\n\n";

try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Cari produk Power Bank 20000mAh
    $stmt = $pdo->prepare("SELECT * FROM products WHERE name LIKE '%Power Bank 20000mAh%'");
    $stmt->execute();
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        echo "❌ Produk Power Bank 20000mAh tidak ditemukan!\n\n";
        
        // Tampilkan produk yang mengandung kata "Power Bank"
        echo "🔍 Mencari produk yang mengandung 'Power Bank'...\n";
        $stmt = $pdo->prepare("SELECT id, name, sku FROM products WHERE name LIKE '%Power Bank%'");
        $stmt->execute();
        $relatedProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($relatedProducts)) {
            echo "📋 Produk terkait yang ditemukan:\n";
            foreach ($relatedProducts as $prod) {
                echo "   - ID: {$prod['id']} | {$prod['name']} | SKU: {$prod['sku']}\n";
            }
        } else {
            echo "   Tidak ada produk Power Bank yang ditemukan.\n";
        }
        
        exit;
    }

    echo "✅ Produk ditemukan:\n";
    echo "   - Nama: {$product['name']}\n";
    echo "   - ID: {$product['id']}\n";
    echo "   - SKU: {$product['sku']}\n";
    echo "   - Harga: Rp " . number_format($product['price'], 0, ',', '.') . "\n";
    if ($product['sale_price']) {
        echo "   - Harga Diskon: Rp " . number_format($product['sale_price'], 0, ',', '.') . "\n";
    }
    echo "   - Stok: {$product['stock_quantity']}\n\n";
    
    // Cek apakah produk memiliki order items
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM order_items WHERE product_id = ?");
    $stmt->execute([$product['id']]);
    $orderItemsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($orderItemsCount > 0) {
        echo "❌ Tidak dapat menghapus produk Power Bank 20000mAh!\n";
        echo "   Produk ini memiliki {$orderItemsCount} order item(s).\n";
        echo "   Produk sudah pernah dipesan oleh pelanggan.\n\n";
        
        echo "💡 Alternatif:\n";
        echo "   1. Nonaktifkan produk (jika fitur tersedia)\n";
        echo "   2. Ubah status stok menjadi 'Habis'\n";
        echo "   3. Sembunyikan dari katalog\n";
        echo "   4. Tandai sebagai discontinued\n";
        
        exit;
    }

    echo "🔍 Memeriksa dependensi...\n";
    echo "✅ Tidak ada order yang terkait dengan produk ini.\n\n";
    echo "🗑️  Menghapus produk Power Bank 20000mAh...\n";
    
    // Hapus produk
    $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
    $stmt->execute([$product['id']]);
    
    $productPrice = 'Rp ' . number_format($product['price'], 0, ',', '.');
    
    echo "✅ Produk '{$product['name']}' berhasil dihapus!\n";
    echo "   SKU: {$product['sku']}\n";
    echo "   Harga: {$productPrice}\n\n";
    
    // Show remaining products count
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products");
    $stmt->execute();
    $remainingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "📦 Total produk tersisa: {$remainingCount}\n";
    
    // Show remaining Electronics products
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE name LIKE '%Electronics%'");
    $stmt->execute();
    $electronicsCategory = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($electronicsCategory) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE category_id = ?");
        $stmt->execute([$electronicsCategory['id']]);
        $electronicsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "📱 Total produk Electronics tersisa: {$electronicsCount}\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🏁 Proses penghapusan selesai!\n";
