@extends('frontend.layout')

@section('title', 'Konfirmasi Pesanan - Sahabat Rumah')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Success Header -->
    <div class="text-center mb-8">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <i class="fas fa-check text-green-600 text-2xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-navy-blue mb-2">P<PERSON><PERSON>konfirma<PERSON>!</h1>
        <p class="text-gray-600">Terima kasih atas pembelian Anda. Pesanan Anda telah berhasil dibuat.</p>
    </div>

    <!-- Order Details Card -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8 border-2 border-light-orange">
        <!-- Order Header -->
        <div class="bg-light-orange px-6 py-4 border-b border-pastel-orange">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-navy-blue">Pesanan #{{ $order->order_number }}</h2>
                    <p class="text-sm text-gray-600">Dibuat pada {{ $order->created_at->format('d M Y \p\u\k\u\l H:i') }}</p>
                </div>
                <div class="mt-2 sm:mt-0">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        {{ ucfirst($order->status) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Order Items -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Order Items</h3>
                    <div class="space-y-4">
                        @foreach($order->orderItems as $item)
                            <div class="flex items-center space-x-4">
                                @if($item->product && $item->product->main_image)
                                    <img src="{{ $item->product->main_image }}" 
                                         alt="{{ $item->product->name }}" 
                                         class="w-16 h-16 object-cover rounded-md">
                                @else
                                    <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                @endif
                                
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">
                                        @if($item->product)
                                            {{ $item->product->name }}
                                        @else
                                            {{ $item->product_name ?? 'Product not found' }}
                                        @endif
                                    </h4>
                                    <p class="text-sm text-gray-600">
                                        Quantity: {{ $item->quantity }} × ${{ number_format($item->price, 2) }}
                                    </p>
                                </div>
                                
                                <div class="text-right">
                                    <p class="font-semibold text-gray-800">
                                        ${{ number_format($item->quantity * $item->price, 2) }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Order Totals -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Order Summary</h3>
                    <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-semibold">${{ number_format($order->subtotal ?? ($order->total_amount - $order->tax_amount - $order->shipping_amount), 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Shipping</span>
                            <span class="font-semibold">${{ number_format($order->shipping_amount ?? 0, 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tax</span>
                            <span class="font-semibold">${{ number_format($order->tax_amount ?? 0, 2) }}</span>
                        </div>
                        <div class="border-t pt-2">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold">Total</span>
                                <span class="text-lg font-bold text-green-600">${{ number_format($order->total_amount, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shipping & Billing Information -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <!-- Shipping Information -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Shipping Information</h3>
            <div class="text-sm text-gray-600 space-y-1">
                <p class="font-medium text-gray-800">{{ $order->customer_name }}</p>
                <p>{{ $order->shipping_address }}</p>
                <p>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}</p>
                @if($order->shipping_country)
                    <p>{{ $order->shipping_country }}</p>
                @endif
            </div>
        </div>

        <!-- Billing Information -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Billing Information</h3>
            <div class="text-sm text-gray-600 space-y-1">
                <p class="font-medium text-gray-800">{{ $order->customer_name }}</p>
                @if($order->billing_address)
                    <p>{{ $order->billing_address }}</p>
                    <p>{{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_postal_code }}</p>
                    @if($order->billing_country)
                        <p>{{ $order->billing_country }}</p>
                    @endif
                @else
                    <p>{{ $order->shipping_address }}</p>
                    <p>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}</p>
                    @if($order->shipping_country)
                        <p>{{ $order->shipping_country }}</p>
                    @endif
                @endif
            </div>
        </div>
    </div>

    <!-- What's Next -->
    <div class="bg-blue-50 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">What's Next?</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-envelope text-white text-sm"></i>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800">Order Confirmation</h4>
                    <p class="text-sm text-gray-600">You'll receive an email confirmation shortly with your order details.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-cog text-white text-sm"></i>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800">Processing</h4>
                    <p class="text-sm text-gray-600">We'll start processing your order within 1-2 business days.</p>
                </div>
            </div>

            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-truck text-white text-sm"></i>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800">Shipping</h4>
                    <p class="text-sm text-gray-600">You'll receive tracking information once your order ships.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Notes -->
    @if($order->notes)
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-navy-blue mb-4">Catatan Pesanan</h3>
            <p class="text-gray-600">{{ $order->notes }}</p>
        </div>
    @endif

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{{ route('account.orders.show', $order) }}"
           class="bg-pastel-orange text-navy-blue px-8 py-3 rounded-lg font-semibold hover:bg-orange-300 transition duration-300 text-center">
            Lihat Detail Pesanan
        </a>
        <a href="{{ route('products.index') }}"
           class="bg-gray-200 text-gray-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-300 transition duration-300 text-center">
            Lanjutkan Belanja
        </a>
    </div>

    <!-- Contact Information -->
    <div class="text-center mt-8 p-6 bg-light-orange rounded-lg border border-pastel-orange">
        <h3 class="text-lg font-semibold text-navy-blue mb-2">Butuh Bantuan?</h3>
        <p class="text-gray-600 mb-4">Jika Anda memiliki pertanyaan tentang pesanan Anda, jangan ragu untuk menghubungi kami.</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center text-sm">
            <a href="mailto:<EMAIL>" class="text-pastel-orange hover:text-orange-400">
                <i class="fas fa-envelope mr-2"></i><EMAIL>
            </a>
            <a href="tel:+62-21-123-4567" class="text-pastel-orange hover:text-orange-400">
                <i class="fas fa-phone mr-2"></i>+62 21 123 4567
            </a>
        </div>
    </div>
</div>
@endsection
