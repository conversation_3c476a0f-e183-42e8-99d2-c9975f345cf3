@extends('frontend.layout')

@section('title', 'My Orders - E-Store')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800">My Orders</h1>
        <p class="text-gray-600">Track and manage your orders</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Navigation -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <nav class="space-y-2">
                    <a href="{{ route('account.dashboard') }}" 
                       class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>
                    <a href="{{ route('account.orders') }}" 
                       class="flex items-center px-3 py-2 text-blue-600 bg-blue-50 rounded-md">
                        <i class="fas fa-shopping-bag mr-3"></i>
                        My Orders
                    </a>
                    <a href="{{ route('account.profile') }}" 
                       class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-user mr-3"></i>
                        Profile
                    </a>
                    <form action="{{ route('logout') }}" method="POST" class="mt-4">
                        @csrf
                        <button type="submit" 
                                class="flex items-center w-full px-3 py-2 text-red-600 hover:bg-red-50 rounded-md">
                            <i class="fas fa-sign-out-alt mr-3"></i>
                            Logout
                        </button>
                    </form>
                </nav>
            </div>
        </div>

        <!-- Orders List -->
        <div class="lg:col-span-3">
            @if($orders->count() > 0)
                <div class="space-y-6">
                    @foreach($orders as $order)
                        <div class="bg-white rounded-lg shadow-md overflow-hidden">
                            <!-- Order Header -->
                            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-800">
                                            Order #{{ $order->order_number }}
                                        </h3>
                                        <p class="text-sm text-gray-600">
                                            Placed on {{ $order->created_at->format('M d, Y \a\t g:i A') }}
                                        </p>
                                    </div>
                                    <div class="mt-2 sm:mt-0 flex items-center space-x-4">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                                            @elseif($order->status === 'shipped') bg-purple-100 text-purple-800
                                            @elseif($order->status === 'completed') bg-green-100 text-green-800
                                            @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800
                                            @endif">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                        <span class="text-lg font-bold text-gray-800">
                                            Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Order Items -->
                            <div class="p-6">
                                <div class="space-y-4">
                                    @foreach($order->orderItems as $item)
                                        <div class="flex items-center space-x-4">
                                            @if($item->product && $item->product->main_image)
                                                <img src="{{ $item->product->main_image }}" 
                                                     alt="{{ $item->product->name }}" 
                                                     class="w-16 h-16 object-cover rounded-md">
                                            @else
                                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400"></i>
                                                </div>
                                            @endif
                                            
                                            <div class="flex-1">
                                                <h4 class="font-medium text-gray-800">
                                                    @if($item->product)
                                                        <a href="{{ route('products.show', $item->product->slug) }}" 
                                                           class="hover:text-blue-600">
                                                            {{ $item->product->name }}
                                                        </a>
                                                    @else
                                                        {{ $item->product_name ?? 'Product not found' }}
                                                    @endif
                                                </h4>
                                                <p class="text-sm text-gray-600">
                                                    Quantity: {{ $item->quantity }} × Rp {{ number_format($item->price, 0, ',', '.') }}
                                                </p>
                                            </div>
                                            
                                            <div class="text-right">
                                                <p class="font-semibold text-gray-800">
                                                    Rp {{ number_format($item->quantity * $item->price, 0, ',', '.') }}
                                                </p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <!-- Order Summary -->
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <div class="flex justify-between items-center">
                                        <div class="space-y-1">
                                            <p class="text-sm text-gray-600">
                                                <span class="font-medium">Shipping Address:</span>
                                                {{ $order->shipping_address }}, {{ $order->shipping_city }}, 
                                                {{ $order->shipping_state }} {{ $order->shipping_postal_code }}
                                            </p>
                                            @if($order->notes)
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium">Notes:</span> {{ $order->notes }}
                                                </p>
                                            @endif
                                        </div>
                                        <a href="{{ route('account.orders.show', $order) }}" 
                                           class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-300">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    {{ $orders->links() }}
                </div>
            @else
                <div class="bg-white rounded-lg shadow-md p-12 text-center">
                    <i class="fas fa-shopping-bag text-gray-400 text-6xl mb-6"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-4">No orders found</h3>
                    <p class="text-gray-500 mb-8">You haven't placed any orders yet. Start shopping to see your orders here.</p>
                    <a href="{{ route('products.index') }}" 
                       class="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition duration-300">
                        Start Shopping
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
