<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class CheckoutController extends Controller
{
    public function index(): View
    {
        $cart = session()->get('cart', []);
        
        if (empty($cart)) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty.');
        }

        $cartItems = [];
        $subtotal = 0;

        foreach ($cart as $id => $details) {
            $product = Product::find($id);
            if ($product) {
                $cartItems[] = [
                    'id' => $id,
                    'product' => $product,
                    'quantity' => $details['quantity'],
                    'price' => $product->current_price,
                    'subtotal' => $product->current_price * $details['quantity']
                ];
                $subtotal += $product->current_price * $details['quantity'];
            }
        }

        $tax = $subtotal * 0.11; // 11% tax (PPN Indonesia)
        $shipping = $subtotal > 100000 ? 0 : 15000; // Free shipping over Rp 100,000
        $total = $subtotal + $tax + $shipping;

        return view('frontend.checkout.index', compact('cartItems', 'subtotal', 'tax', 'shipping', 'total'));
    }

    public function process(Request $request): RedirectResponse
    {
        $cart = session()->get('cart', []);
        
        if (empty($cart)) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty.');
        }

        $validated = $request->validate([
            'shipping_address' => 'required|string|max:255',
            'shipping_city' => 'required|string|max:100',
            'shipping_state' => 'required|string|max:100',
            'shipping_postal_code' => 'required|string|max:20',
            'shipping_country' => 'required|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        return DB::transaction(function () use ($validated, $cart, $request) {
            $user = auth()->user();
            $subtotal = 0;

            // Calculate totals
            foreach ($cart as $id => $details) {
                $product = Product::find($id);
                if ($product) {
                    $subtotal += $product->current_price * $details['quantity'];
                }
            }

            $tax = $subtotal * 0.11;
            $shipping = $subtotal > 100000 ? 0 : 15000;
            $total = $subtotal + $tax + $shipping;

            // Create order
            $order = Order::create([
                'order_number' => 'ORD-' . strtoupper(uniqid()),
                'customer_name' => $user->name,
                'customer_email' => $user->email,
                'customer_phone' => $user->phone,
                'status' => 'pending',
                'subtotal' => $subtotal,
                'tax_amount' => $tax,
                'shipping_amount' => $shipping,
                'total_amount' => $total,
                'shipping_address' => $validated['shipping_address'],
                'shipping_city' => $validated['shipping_city'],
                'shipping_state' => $validated['shipping_state'],
                'shipping_postal_code' => $validated['shipping_postal_code'],
                'shipping_country' => $validated['shipping_country'],
                'notes' => $validated['notes'] ?? null,
            ]);

            // Create order items and update stock
            foreach ($cart as $id => $details) {
                $product = Product::find($id);
                if ($product) {
                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $id,
                        'quantity' => $details['quantity'],
                        'price' => $product->current_price,
                        'total' => $product->current_price * $details['quantity'],
                    ]);

                    // Decrease stock
                    $product->decreaseStock($details['quantity']);
                }
            }

            // Clear cart
            session()->forget('cart');

            return redirect()->route('checkout.success', $order)
                ->with('success', 'Order placed successfully!');
        });
    }

    public function success(Order $order): View
    {
        // Ensure user can only see their own orders
        if ($order->customer_email !== auth()->user()->email) {
            abort(403);
        }

        $order->load('orderItems.product');

        return view('frontend.checkout.success', compact('order'));
    }
}
