<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AddProductImagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update semua produk dengan gambar yang menarik
        $updates = [
            // Update berdasarkan nama produk
            ['name' => 'iPhone 15 Pro', 'images' => '["https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=500&h=500&fit=crop"]'],
            ['name' => 'Samsung Galaxy S24', 'images' => '["https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500&h=500&fit=crop"]'],
            ['name' => 'Google Pixel 8', 'images' => '["https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=500&h=500&fit=crop"]'],
            ['name' => 'MacBook Pro 16"', 'images' => '["https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500&h=500&fit=crop"]'],
            ['name' => 'Dell XPS 13', 'images' => '["https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=500&fit=crop"]'],
            ['name' => 'Gaming Mouse RGB', 'images' => '["https://images.unsplash.com/photo-1527814050087-3793815479db?w=500&h=500&fit=crop"]'],
            ['name' => 'Mechanical Keyboard RGB', 'images' => '["https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500&h=500&fit=crop"]'],
            ['name' => 'Wireless Earbuds Pro', 'images' => '["https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=500&h=500&fit=crop"]'],
            ['name' => 'USB-C Hub Multiport', 'images' => '["https://images.unsplash.com/photo-*************-8f3296236761?w=500&h=500&fit=crop"]'],
            ['name' => 'Bluetooth Speaker', 'images' => '["https://images.unsplash.com/photo-*************-423dbba4e7e1?w=500&h=500&fit=crop"]'],
            ['name' => 'Wireless Charger', 'images' => '["https://images.unsplash.com/photo-*************-b95a79798f07?w=500&h=500&fit=crop"]'],
            ['name' => 'Power Bank 20000mAh', 'images' => '["https://images.unsplash.com/photo-*************-4d1b5e5e6e8e?w=500&h=500&fit=crop"]'],
            ['name' => 'Webcam HD 1080p', 'images' => '["https://images.unsplash.com/photo-*************-dfaf72ae4b04?w=500&h=500&fit=crop"]'],
            ['name' => 'Sony WH-1000XM5', 'images' => '["https://images.unsplash.com/photo-*************-acd977736f90?w=500&h=500&fit=crop"]'],
            ['name' => 'Gaming Headset', 'images' => '["https://images.unsplash.com/photo-*************-************?w=500&h=500&fit=crop"]'],
            ['name' => 'iPad Pro 12.9"', 'images' => '["https://images.unsplash.com/photo-**********-0df4b3ffc6b0?w=500&h=500&fit=crop"]'],
            ['name' => 'Samsung Galaxy Tab S9', 'images' => '["https://images.unsplash.com/photo-**********-82e9adf32764?w=500&h=500&fit=crop"]'],
            ['name' => 'Apple Watch Series 9', 'images' => '["https://images.unsplash.com/photo-*************-2f02dc6ca35d?w=500&h=500&fit=crop"]'],
            ['name' => 'Samsung Galaxy Watch 6', 'images' => '["https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop"]'],
            ['name' => 'Canon EOS R5', 'images' => '["https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=500&h=500&fit=crop"]'],
            ['name' => 'Sony Alpha A7 IV', 'images' => '["https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=500&h=500&fit=crop"]'],
            ['name' => 'Gaming Chair RGB', 'images' => '["https://images.unsplash.com/photo-1592300103777-e6e5b8b8e8e8?w=500&h=500&fit=crop"]'],
            ['name' => 'Monitor 27" 4K', 'images' => '["https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=500&h=500&fit=crop"]'],
            ['name' => 'Desk Lamp LED', 'images' => '["https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop"]'],
            ['name' => 'Office Chair Ergonomic', 'images' => '["https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500&h=500&fit=crop"]'],
            ['name' => 'Laptop Stand Adjustable', 'images' => '["https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500&h=500&fit=crop"]'],
        ];

        $updatedCount = 0;

        foreach ($updates as $update) {
            $affected = DB::table('products')
                ->where('name', 'like', '%' . $update['name'] . '%')
                ->update(['images' => $update['images']]);

            if ($affected > 0) {
                echo "✅ Updated images for: {$update['name']}\n";
                $updatedCount += $affected;
            }
        }

        // Update produk yang belum punya gambar dengan gambar default
        $defaultImages = [
            'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=500&fit=crop',
            'https://images.unsplash.com/photo-1526738549149-8e07eca6c147?w=500&h=500&fit=crop',
            'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=500&h=500&fit=crop',
            'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=500&fit=crop',
            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop',
        ];

        $productsWithoutImages = DB::table('products')
            ->whereNull('images')
            ->orWhere('images', '[]')
            ->orWhere('images', 'null')
            ->orWhere('images', '')
            ->get();

        foreach ($productsWithoutImages as $index => $product) {
            $defaultImage = $defaultImages[$index % count($defaultImages)];
            DB::table('products')
                ->where('id', $product->id)
                ->update(['images' => '["' . $defaultImage . '"]']);

            echo "✅ Added default image for: {$product->name}\n";
            $updatedCount++;
        }

        echo "\n🎉 Successfully updated images for {$updatedCount} products!\n";
        echo "📸 All products now have attractive product images from Unsplash.\n";
    }
}
