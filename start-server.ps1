# Laravel E-Commerce Server Starter Script
Write-Host "Starting Laravel E-Commerce Server..." -ForegroundColor Green
Write-Host ""

# Set PHP path from Herd
$phpPath = "C:\Users\<USER>\.config\herd\bin\php84\php.exe"

# Check if PHP exists
if (-not (Test-Path $phpPath)) {
    Write-Host "Error: PHP not found at $phpPath" -ForegroundColor Red
    Write-Host "Please check your Herd installation." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "PHP found: $phpPath" -ForegroundColor Green
Write-Host ""

# Clear Laravel cache
Write-Host "Clearing Laravel cache..." -ForegroundColor Yellow
try {
    & $phpPath artisan cache:clear
    & $phpPath artisan config:clear  
    & $phpPath artisan view:clear
    Write-Host "Cache cleared successfully!" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not clear cache. Continuing..." -ForegroundColor Yellow
}
Write-Host ""

# Display access URLs
Write-Host "Server will be available at:" -ForegroundColor Cyan
Write-Host "  - http://localhost:8000 (PHP built-in server)" -ForegroundColor White
Write-Host "  - http://ecommerce.test (Herd domain)" -ForegroundColor White
Write-Host ""
Write-Host "Admin dashboard:" -ForegroundColor Cyan
Write-Host "  - http://localhost:8000/admin" -ForegroundColor White
Write-Host "  - http://ecommerce.test/admin" -ForegroundColor White
Write-Host ""

Write-Host "Starting server on http://localhost:8000..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Start the server
try {
    & $phpPath -S localhost:8000 -t public
} catch {
    Write-Host "Error starting server: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
