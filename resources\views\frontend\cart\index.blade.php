@extends('frontend.layout')

@section('title', 'Keranjang Belanja - Sahabat Rumah')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-navy-blue mb-8">Keranjang Belanja</h1>

    @if(count($cartItems) > 0)
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Cart Items -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    @foreach($cartItems as $item)
                        <div class="p-6 border-b border-gray-200 last:border-b-0" id="cart-item-{{ $item['id'] }}">
                            <div class="flex items-center space-x-4">
                                <!-- Product Image -->
                                <div class="flex-shrink-0">
                                    @if($item['product']->main_image)
                                        <img src="{{ $item['product']->main_image }}" alt="{{ $item['product']->name }}" 
                                             class="w-20 h-20 object-cover rounded-md">
                                    @else
                                        <div class="w-20 h-20 bg-gray-200 rounded-md flex items-center justify-center">
                                            <i class="fas fa-image text-gray-400"></i>
                                        </div>
                                    @endif
                                </div>

                                <!-- Product Details -->
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-800">
                                        <a href="{{ route('products.show', $item['product']->slug) }}" class="hover:text-blue-600">
                                            {{ $item['product']->name }}
                                        </a>
                                    </h3>
                                    @if($item['product']->category)
                                        <p class="text-sm text-gray-600">{{ $item['product']->category->name }}</p>
                                    @endif
                                    <p class="text-lg font-bold text-gray-800 mt-1">Rp {{ number_format($item['price'], 0, ',', '.') }}</p>
                                </div>

                                <!-- Quantity Controls -->
                                <div class="flex items-center space-x-2">
                                    <button onclick="updateQuantity({{ $item['id'] }}, {{ $item['quantity'] - 1 }})" 
                                            class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition duration-300"
                                            {{ $item['quantity'] <= 1 ? 'disabled' : '' }}>
                                        <i class="fas fa-minus text-xs"></i>
                                    </button>
                                    <span class="w-12 text-center font-semibold" id="quantity-{{ $item['id'] }}">{{ $item['quantity'] }}</span>
                                    <button onclick="updateQuantity({{ $item['id'] }}, {{ $item['quantity'] + 1 }})" 
                                            class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition duration-300">
                                        <i class="fas fa-plus text-xs"></i>
                                    </button>
                                </div>

                                <!-- Subtotal -->
                                <div class="text-right">
                                    <p class="text-lg font-bold text-gray-800" id="subtotal-{{ $item['id'] }}">
                                        Rp {{ number_format($item['subtotal'], 0, ',', '.') }}
                                    </p>
                                </div>

                                <!-- Remove Button -->
                                <button onclick="removeItem({{ $item['id'] }})" 
                                        class="text-red-600 hover:text-red-800 transition duration-300">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Continue Shopping -->
                <div class="mt-6">
                    <a href="{{ route('products.index') }}"
                       class="inline-flex items-center text-pastel-orange hover:text-orange-400">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Lanjutkan Belanja
                    </a>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-24 border-2 border-light-orange">
                    <h2 class="text-xl font-semibold text-navy-blue mb-4">Ringkasan Pesanan</h2>

                    <div class="space-y-3 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="font-semibold" id="cart-subtotal">Rp {{ number_format($total, 0, ',', '.') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Ongkos Kirim</span>
                            <span class="font-semibold">
                                @if($total >= 500000)
                                    <span class="text-green-600">Gratis</span>
                                @else
                                    Rp {{ number_format(15000, 0, ',', '.') }}
                                @endif
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Pajak (11%)</span>
                            <span class="font-semibold">Rp {{ number_format($total * 0.11, 0, ',', '.') }}</span>
                        </div>
                        <div class="border-t pt-3">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-navy-blue">Total</span>
                                <span class="text-lg font-bold text-pastel-orange" id="cart-total">
                                    Rp {{ number_format($total + ($total >= 500000 ? 0 : 15000) + ($total * 0.11), 0, ',', '.') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    @if($total >= 500000)
                        <div class="bg-green-100 text-green-800 p-3 rounded-md mb-4 text-sm">
                            <i class="fas fa-check-circle mr-2"></i>
                            Anda mendapat gratis ongkos kirim!
                        </div>
                    @else
                        <div class="bg-light-orange text-navy-blue p-3 rounded-md mb-4 text-sm">
                            <i class="fas fa-info-circle mr-2"></i>
                            Tambah Rp {{ number_format(500000 - $total, 0, ',', '.') }} lagi untuk gratis ongkir
                        </div>
                    @endif

                    @auth
                        <a href="{{ route('checkout.index') }}"
                           class="w-full bg-pastel-orange text-navy-blue py-3 px-6 rounded-lg text-center font-semibold hover:bg-orange-300 transition duration-300 block">
                            Lanjut ke Checkout
                        </a>
                    @else
                        <div class="space-y-3">
                            <a href="{{ route('login') }}"
                               class="w-full bg-pastel-orange text-navy-blue py-3 px-6 rounded-lg text-center font-semibold hover:bg-orange-300 transition duration-300 block">
                                Masuk untuk Checkout
                            </a>
                            <p class="text-sm text-gray-600 text-center">
                                Belum punya akun?
                                <a href="{{ route('register') }}" class="text-pastel-orange hover:text-orange-400">Daftar</a>
                            </p>
                        </div>
                    @endauth

                    <!-- Clear Cart -->
                    <button onclick="clearCart()"
                            class="w-full mt-4 text-red-600 hover:text-red-800 text-sm">
                        Kosongkan Keranjang
                    </button>
                </div>
            </div>
        </div>
    @else
        <!-- Empty Cart -->
        <div class="text-center py-16">
            <div class="max-w-md mx-auto">
                <i class="fas fa-shopping-cart text-pastel-orange text-6xl mb-6"></i>
                <h2 class="text-2xl font-semibold text-navy-blue mb-4">Keranjang Anda kosong</h2>
                <p class="text-gray-500 mb-8">Sepertinya Anda belum menambahkan produk ke keranjang.</p>
                <a href="{{ route('products.index') }}"
                   class="bg-pastel-orange text-navy-blue px-8 py-3 rounded-lg text-lg font-semibold hover:bg-orange-300 transition duration-300">
                    Mulai Belanja
                </a>
            </div>
        </div>
    @endif
</div>

<script>
async function updateQuantity(productId, newQuantity) {
    if (newQuantity < 1) return;
    
    try {
        const response = await fetch(`/cart/update/${productId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ quantity: newQuantity })
        });

        const data = await response.json();
        
        if (response.ok) {
            // Update quantity display
            document.getElementById(`quantity-${productId}`).textContent = newQuantity;
            
            // Update subtotal
            document.getElementById(`subtotal-${productId}`).textContent = `Rp ${new Intl.NumberFormat('id-ID').format(data.subtotal)}`;

            // Update cart total
            document.getElementById('cart-total').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(data.total)}`;
            
            // Update cart count in navigation
            const cartCountElement = document.querySelector('[x-text="cartCount"]');
            if (cartCountElement) {
                // Recalculate total cart count
                const quantities = Array.from(document.querySelectorAll('[id^="quantity-"]'))
                    .map(el => parseInt(el.textContent));
                const totalCount = quantities.reduce((sum, qty) => sum + qty, 0);
                cartCountElement.__x.$data.cartCount = totalCount;
            }
            
            showNotification('Keranjang berhasil diperbarui', 'success');
        } else {
            showNotification(data.message, 'error');
        }
    } catch (error) {
        showNotification('Terjadi kesalahan', 'error');
    }
}

async function removeItem(productId) {
    if (!confirm('Apakah Anda yakin ingin menghapus produk ini dari keranjang?')) {
        return;
    }
    
    try {
        const response = await fetch(`/cart/remove/${productId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();
        
        if (response.ok) {
            // Remove item from DOM
            document.getElementById(`cart-item-${productId}`).remove();
            
            // Update cart total
            document.getElementById('cart-total').textContent = `Rp ${new Intl.NumberFormat('id-ID').format(data.total)}`;
            
            // Update cart count in navigation
            const cartCountElement = document.querySelector('[x-text="cartCount"]');
            if (cartCountElement) {
                cartCountElement.__x.$data.cartCount = data.cart_count;
            }
            
            // Check if cart is empty and reload page
            if (data.cart_count === 0) {
                location.reload();
            }
            
            showNotification('Produk dihapus dari keranjang', 'success');
        } else {
            showNotification(data.message, 'error');
        }
    } catch (error) {
        showNotification('Terjadi kesalahan', 'error');
    }
}

async function clearCart() {
    if (!confirm('Apakah Anda yakin ingin mengosongkan seluruh keranjang?')) {
        return;
    }
    
    try {
        const response = await fetch('/cart/clear', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();
        
        if (response.ok) {
            location.reload();
        } else {
            showNotification(data.message, 'error');
        }
    } catch (error) {
        showNotification('Terjadi kesalahan', 'error');
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
