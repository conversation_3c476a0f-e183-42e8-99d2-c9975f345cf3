@extends('frontend.layout')

@section('title', 'Checkout - Sahabat Rumah')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
            <li><a href="{{ route('home') }}" class="hover:text-pastel-orange">Beranda</a></li>
            <li><i class="fas fa-chevron-right text-xs"></i></li>
            <li><a href="{{ route('cart.index') }}" class="hover:text-pastel-orange">Keranjang</a></li>
            <li><i class="fas fa-chevron-right text-xs"></i></li>
            <li class="text-navy-blue font-medium">Checkout</li>
        </ol>
    </nav>

    <div class="mb-8">
        <h1 class="text-3xl font-bold text-navy-blue">Checkout</h1>
        <p class="text-gray-600"><PERSON><PERSON>aikan pesanan Anda</p>
    </div>

    <form action="{{ route('checkout.process') }}" method="POST" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        @csrf
        
        <!-- Checkout Form -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow-md p-6 border-2 border-light-orange">
                <h2 class="text-xl font-semibold text-navy-blue mb-6">Informasi Pelanggan</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-navy-blue mb-2">Nama Lengkap *</label>
                        <input type="text" id="customer_name" name="customer_name"
                               value="{{ old('customer_name', auth()->user()->name) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('customer_name') border-red-500 @enderror">
                        @error('customer_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-navy-blue mb-2">Alamat Email *</label>
                        <input type="email" id="customer_email" name="customer_email"
                               value="{{ old('customer_email', auth()->user()->email) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('customer_email') border-red-500 @enderror">
                        @error('customer_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="customer_phone" class="block text-sm font-medium text-navy-blue mb-2">Nomor Telepon</label>
                        <input type="tel" id="customer_phone" name="customer_phone"
                               value="{{ old('customer_phone', auth()->user()->phone) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('customer_phone') border-red-500 @enderror">
                        @error('customer_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            <div class="bg-white rounded-lg shadow-md p-6 border-2 border-light-orange">
                <h2 class="text-xl font-semibold text-navy-blue mb-6">Alamat Pengiriman</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="shipping_address" class="block text-sm font-medium text-navy-blue mb-2">Alamat Lengkap *</label>
                        <input type="text" id="shipping_address" name="shipping_address"
                               value="{{ old('shipping_address', auth()->user()->shipping_address) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('shipping_address') border-red-500 @enderror">
                        @error('shipping_address')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="shipping_city" class="block text-sm font-medium text-navy-blue mb-2">Kota *</label>
                        <input type="text" id="shipping_city" name="shipping_city"
                               value="{{ old('shipping_city', auth()->user()->shipping_city) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('shipping_city') border-red-500 @enderror">
                        @error('shipping_city')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="shipping_state" class="block text-sm font-medium text-navy-blue mb-2">Provinsi *</label>
                        <input type="text" id="shipping_state" name="shipping_state"
                               value="{{ old('shipping_state', auth()->user()->shipping_state) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('shipping_state') border-red-500 @enderror">
                        @error('shipping_state')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="shipping_postal_code" class="block text-sm font-medium text-navy-blue mb-2">Kode Pos *</label>
                        <input type="text" id="shipping_postal_code" name="shipping_postal_code"
                               value="{{ old('shipping_postal_code', auth()->user()->shipping_postal_code) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('shipping_postal_code') border-red-500 @enderror">
                        @error('shipping_postal_code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="shipping_country" class="block text-sm font-medium text-navy-blue mb-2">Negara *</label>
                        <input type="text" id="shipping_country" name="shipping_country"
                               value="{{ old('shipping_country', auth()->user()->shipping_country ?? 'Indonesia') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange @error('shipping_country') border-red-500 @enderror">
                        @error('shipping_country')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Billing Address -->
            <div class="bg-white rounded-lg shadow-md p-6 border-2 border-light-orange">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-navy-blue">Alamat Penagihan</h2>
                    <label class="flex items-center">
                        <input type="checkbox" id="same_as_shipping" name="same_as_shipping" value="1"
                               {{ old('same_as_shipping') ? 'checked' : '' }}
                               class="mr-2 text-pastel-orange focus:ring-pastel-orange">
                        <span class="text-sm text-gray-600">Sama dengan alamat pengiriman</span>
                    </label>
                </div>
                
                <div id="billing_fields" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="billing_address" class="block text-sm font-medium text-gray-700 mb-2">Street Address *</label>
                        <input type="text" id="billing_address" name="billing_address" 
                               value="{{ old('billing_address', auth()->user()->billing_address) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('billing_address') border-red-500 @enderror">
                        @error('billing_address')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="billing_city" class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                        <input type="text" id="billing_city" name="billing_city" 
                               value="{{ old('billing_city', auth()->user()->billing_city) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('billing_city') border-red-500 @enderror">
                        @error('billing_city')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="billing_state" class="block text-sm font-medium text-gray-700 mb-2">State/Province *</label>
                        <input type="text" id="billing_state" name="billing_state" 
                               value="{{ old('billing_state', auth()->user()->billing_state) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('billing_state') border-red-500 @enderror">
                        @error('billing_state')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="billing_postal_code" class="block text-sm font-medium text-gray-700 mb-2">Postal Code *</label>
                        <input type="text" id="billing_postal_code" name="billing_postal_code" 
                               value="{{ old('billing_postal_code', auth()->user()->billing_postal_code) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('billing_postal_code') border-red-500 @enderror">
                        @error('billing_postal_code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="billing_country" class="block text-sm font-medium text-gray-700 mb-2">Country *</label>
                        <input type="text" id="billing_country" name="billing_country" 
                               value="{{ old('billing_country', auth()->user()->billing_country ?? 'United States') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('billing_country') border-red-500 @enderror">
                        @error('billing_country')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Payment Method -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-6">Payment Method</h2>
                
                <div class="space-y-4">
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="credit_card" checked
                               class="mr-3 text-blue-600 focus:ring-blue-500">
                        <div class="flex items-center">
                            <i class="fas fa-credit-card text-gray-600 mr-3"></i>
                            <span class="font-medium">Credit Card</span>
                        </div>
                    </label>

                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="paypal"
                               class="mr-3 text-blue-600 focus:ring-blue-500">
                        <div class="flex items-center">
                            <i class="fab fa-paypal text-blue-600 mr-3"></i>
                            <span class="font-medium">PayPal</span>
                        </div>
                    </label>

                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="bank_transfer"
                               class="mr-3 text-blue-600 focus:ring-blue-500">
                        <div class="flex items-center">
                            <i class="fas fa-university text-gray-600 mr-3"></i>
                            <span class="font-medium">Bank Transfer</span>
                        </div>
                    </label>
                </div>
            </div>

            <!-- Order Notes -->
            <div class="bg-white rounded-lg shadow-md p-6 border-2 border-light-orange">
                <h2 class="text-xl font-semibold text-navy-blue mb-6">Catatan Pesanan (Opsional)</h2>
                <textarea name="notes" id="notes" rows="4"
                          placeholder="Instruksi khusus untuk pesanan Anda..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pastel-orange">{{ old('notes') }}</textarea>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6 sticky top-8 border-2 border-light-orange">
                <h2 class="text-xl font-semibold text-navy-blue mb-6">Ringkasan Pesanan</h2>
                
                <!-- Cart Items -->
                <div class="space-y-4 mb-6">
                    @foreach($cartItems as $id => $item)
                        <div class="flex items-center space-x-3">
                            @if($item['product']->main_image)
                                <img src="{{ $item['product']->main_image }}" 
                                     alt="{{ $item['product']->name }}" 
                                     class="w-12 h-12 object-cover rounded">
                            @else
                                <div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400"></i>
                                </div>
                            @endif
                            
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-800 text-sm">{{ $item['product']->name }}</h4>
                                <p class="text-xs text-gray-600">Jumlah: {{ $item['quantity'] }}</p>
                            </div>
                            
                            <span class="font-semibold text-gray-800">
                                Rp {{ number_format($item['quantity'] * $item['price'], 0, ',', '.') }}
                            </span>
                        </div>
                    @endforeach
                </div>

                <!-- Order Totals -->
                <div class="border-t pt-4 space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="font-semibold">Rp {{ number_format($subtotal, 0, ',', '.') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Ongkos Kirim</span>
                        <span class="font-semibold">Rp {{ number_format($shipping, 0, ',', '.') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Pajak (11%)</span>
                        <span class="font-semibold">Rp {{ number_format($tax, 0, ',', '.') }}</span>
                    </div>
                    <div class="border-t pt-2">
                        <div class="flex justify-between">
                            <span class="text-lg font-semibold text-navy-blue">Total</span>
                            <span class="text-lg font-bold text-pastel-orange">Rp {{ number_format($total, 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Place Order Button -->
                <button type="submit"
                        class="w-full mt-6 bg-pastel-orange text-navy-blue py-3 px-4 rounded-lg font-semibold hover:bg-orange-300 transition duration-300">
                    <i class="fas fa-lock mr-2"></i>
                    Buat Pesanan
                </button>

                <p class="text-xs text-gray-500 text-center mt-4">
                    Informasi pembayaran Anda aman dan terenkripsi
                </p>
            </div>
        </div>
    </form>
</div>

<script>
document.getElementById('same_as_shipping').addEventListener('change', function() {
    const isChecked = this.checked;
    const fields = ['address', 'city', 'state', 'postal_code', 'country'];
    
    fields.forEach(field => {
        const shippingInput = document.getElementById(`shipping_${field}`);
        const billingInput = document.getElementById(`billing_${field}`);
        
        if (isChecked && shippingInput && billingInput) {
            billingInput.value = shippingInput.value;
            billingInput.readOnly = true;
            billingInput.classList.add('bg-gray-100');
        } else if (billingInput) {
            billingInput.readOnly = false;
            billingInput.classList.remove('bg-gray-100');
        }
    });
});

// Update billing when shipping changes if same_as_shipping is checked
['address', 'city', 'state', 'postal_code', 'country'].forEach(field => {
    const shippingInput = document.getElementById(`shipping_${field}`);
    if (shippingInput) {
        shippingInput.addEventListener('input', function() {
            const sameAsShipping = document.getElementById('same_as_shipping');
            const billingInput = document.getElementById(`billing_${field}`);
            
            if (sameAsShipping.checked && billingInput) {
                billingInput.value = this.value;
            }
        });
    }
});
</script>
@endsection
